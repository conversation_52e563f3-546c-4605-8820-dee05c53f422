//
//  ChatScreenView.swift
//  Pluto AI - Chat <PERSON>t Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI
import Combine
import CoreData
import UniformTypeIdentifiers

// MARK: - Assistant Visit Tracking
extension UserDefaults {
    private static let assistantVisitPrefix = "assistant_visited_"

    func hasVisitedAssistant(_ assistantName: String) -> Bool {
        return bool(forKey: UserDefaults.assistantVisitPrefix + assistantName)
    }

    func markAssistantAsVisited(_ assistantName: String) {
        set(true, forKey: UserDefaults.assistantVisitPrefix + assistantName)
    }
}

@MainActor
struct ChatScreenView: View, ChatStreamingDelegate {
    @StateObject private var chatHistoryService = ChatHistoryService.shared
    @State private var messageText = ""
    @State private var messages: [Message] = []
    @State private var showModelSelection = false
    // Set default model to GPT-4.1 nano
    @State private var selectedModel = AIModel.models.first(where: { $0.name == "GPT-4.1 nano" }) ?? AIModel.models[0]
    @State private var isLoading = false
    @State private var isStreaming = false
    @State private var currentStreamingMessage: Message?
    @State private var refreshTrigger = 0 // Force UI refresh like DefaultChatView
    @State private var errorMessage: String? = nil
    @State private var shouldAutoSend = false
    @State private var initialMessageToSend: String? = nil
    @State private var chatTitle: String = ""
    @State private var selectedImageForGeneration: UIImage? = nil
    @State private var didSendMessage = false
    @State private var showSuggestions = false
    @State private var showTools = false
    @State private var systemMessage: String? = nil
    @State private var categoryName: String? = nil
    @State private var categoryIcon: String? = nil
    @State private var showExamplesPopup = false
    @State private var showAssistantIntro = false
    @State private var forceShowAssistantIntro = false
    @State private var showImageResults = false
    @State private var selectedImageURL: URL?
    @State private var showOptionsDropdown = false
    @State private var currentChatId: String? = nil
    @State private var showChatHistory = false

    // Assistant information for specialized chats
    let assistant: Assistant?
    @State private var categoryExamples: [String] = []
    @State private var documentURL: URL? = nil
    @State private var webURL: URL? = nil
    @FocusState private var isInputFieldFocused: Bool
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext

    // Default initializer
    init(assistant: Assistant? = nil) {
        self.assistant = assistant

        // Check if we have a saved category from a previous session
        if let savedCategory = UserDefaults.standard.string(forKey: "lastSelectedCategory"),
           let savedSystemMessage = UserDefaults.standard.string(forKey: "lastSystemMessage"),
           let savedExamples = UserDefaults.standard.array(forKey: "lastCategoryExamples") as? [String] {

            // Restore the saved category information
            _categoryName = State(initialValue: savedCategory)
            _systemMessage = State(initialValue: savedSystemMessage)
            _categoryExamples = State(initialValue: savedExamples)
            _chatTitle = State(initialValue: savedCategory)
        }
    }

    // Initialize with an initial message and autoSend flag
    init(initialMessage: String, autoSend: Bool, assistant: Assistant? = nil) {
        print("ChatScreenView init: initialMessage='\(initialMessage)', autoSend=\(autoSend)")
        self.assistant = assistant

        // Trim the message to ensure it's not just whitespace
        let trimmedMessage = initialMessage.trimmingCharacters(in: .whitespacesAndNewlines)

        // Use _messageText to set the initial value of the @State property
        _messageText = State(initialValue: trimmedMessage)
        _shouldAutoSend = State(initialValue: autoSend)

        // If autoSend is true, store the initial message to send
        if autoSend && !trimmedMessage.isEmpty {
            print("ChatScreenView: Storing initial message to send: '\(trimmedMessage)'")
            _initialMessageToSend = State(initialValue: trimmedMessage)
        }

        // Check if we have a saved category from a previous session
        if let savedCategory = UserDefaults.standard.string(forKey: "lastSelectedCategory"),
           let savedSystemMessage = UserDefaults.standard.string(forKey: "lastSystemMessage"),
           let savedExamples = UserDefaults.standard.array(forKey: "lastCategoryExamples") as? [String] {

            // Restore the saved category information
            _categoryName = State(initialValue: savedCategory)
            _systemMessage = State(initialValue: savedSystemMessage)
            _categoryExamples = State(initialValue: savedExamples)

            // Only set the chat title if we don't have an initial message
            if trimmedMessage.isEmpty {
                _chatTitle = State(initialValue: savedCategory)
            }
        }
    }

    // Initialize with initial messages
    init(withMessages messages: [Message], assistant: Assistant? = nil) {
        self.assistant = assistant

        // Use _messages to set the initial value of the @State property
        _messages = State(initialValue: messages)

        // Check if we have a saved category from a previous session
        if let savedCategory = UserDefaults.standard.string(forKey: "lastSelectedCategory"),
           let savedSystemMessage = UserDefaults.standard.string(forKey: "lastSystemMessage"),
           let savedExamples = UserDefaults.standard.array(forKey: "lastCategoryExamples") as? [String] {

            // Restore the saved category information
            _categoryName = State(initialValue: savedCategory)
            _systemMessage = State(initialValue: savedSystemMessage)
            _categoryExamples = State(initialValue: savedExamples)
            _chatTitle = State(initialValue: savedCategory)
        }
    }

    // Initialize with a suggestion from a specific category
    init(suggestion: Suggestion, autoSend: Bool = false, showExamplesOnAppear: Bool = false, assistant: Assistant? = nil) {
        print("ChatScreenView init with suggestion: '\(suggestion.text)' from category '\(suggestion.category)'")
        self.assistant = assistant

        // Get the system message for this category
        let systemMsg = suggestion.getSystemMessage()
        _systemMessage = State(initialValue: systemMsg)
        _categoryName = State(initialValue: suggestion.category)
        _categoryIcon = State(initialValue: suggestion.icon)

        // Get examples for this category - use suggestion's examples if available, otherwise use category examples
        let examples = suggestion.examplePrompts.isEmpty ? CategoryExamples.getExamples(for: suggestion.category) : suggestion.examplePrompts
        _categoryExamples = State(initialValue: examples)

        // Set the suggestion text as the initial message if autoSend is true
        if autoSend {
            _messageText = State(initialValue: suggestion.text)
            _initialMessageToSend = State(initialValue: suggestion.text)
            _shouldAutoSend = State(initialValue: true)
        } else {
            // Otherwise, let the user type or select from examples
            _messageText = State(initialValue: "")
            _shouldAutoSend = State(initialValue: false)
        }

        // Set a title based on the category
        _chatTitle = State(initialValue: suggestion.category)

        // Show examples popup on appear if requested
        _showExamplesPopup = State(initialValue: showExamplesOnAppear)

        // We don't need to save to UserDefaults anymore since we're opening directly
        // and not relying on persistence between sessions
    }

    // Initialize with a suggestion and selected image for AI Image Generator
    init(suggestion: Suggestion, autoSend: Bool = false, selectedImage: UIImage? = nil, assistant: Assistant? = nil) {
        print("ChatScreenView init with suggestion: '\(suggestion.text)' from category '\(suggestion.category)' with image: \(selectedImage != nil)")
        self.assistant = assistant

        // Get the system message for this category
        let systemMsg = suggestion.getSystemMessage()
        _systemMessage = State(initialValue: systemMsg)
        _categoryName = State(initialValue: suggestion.category)
        _categoryIcon = State(initialValue: suggestion.icon)

        // Get examples for this category
        let examples = CategoryExamples.getExamples(for: suggestion.category)
        _categoryExamples = State(initialValue: examples)

        // Set the suggestion text as the initial message if autoSend is true
        if autoSend {
            _messageText = State(initialValue: suggestion.text)
            _initialMessageToSend = State(initialValue: suggestion.text)
            _shouldAutoSend = State(initialValue: true)
        } else {
            // Otherwise, let the user type or select from examples
            _messageText = State(initialValue: "")
            _shouldAutoSend = State(initialValue: false)
        }

        // Set a title based on the category
        _chatTitle = State(initialValue: suggestion.category)

        // Store the selected image for image generation/editing
        _selectedImageForGeneration = State(initialValue: selectedImage)
    }

    var body: some View {
        ZStack {
            // Main chat view
            VStack(spacing: 0) {
                // Chat messages with optimized auto-scroll
                ScrollViewReader { proxy in
                    ScrollView {
                        VStack(spacing: 16) {
                            // Initial greeting message with quick action buttons or task header
                            if messages.isEmpty {
                                if let assistant = assistant {
                                    // Show assistant greeting with quick actions
                                    AssistantGreetingView(
                                        assistant: assistant,
                                        onActionSelected: { actionText in
                                            messageText = actionText
                                            sendMessage()
                                        }
                                    )
                                } else if let category = categoryName {
                                    // Check if this is a suggestion category and show appropriate greeting
                                    if let suggestionCategory = SuggestionCategory.categories.first(where: { $0.name == category }) {
                                        // Show suggestion greeting with examples
                                        SuggestionGreetingView(
                                            category: suggestionCategory,
                                            onActionSelected: { actionText in
                                                messageText = actionText
                                                sendMessage()
                                            }
                                        )
                                    } else {
                                        // Show task header for other specialized chats
                                        TaskHeaderView(
                                            categoryName: category,
                                            icon: categoryIcon ?? CategoryExamples.getIcon(for: category),
                                            color: CategoryExamples.getColor(for: category),
                                            assistant: assistant,
                                            onExamplesButtonTapped: {
                                                showExamplesPopup = true
                                            }
                                        )
                                    }
                                } else {
                                    // Show default greeting for general chat
                                    VStack(alignment: .leading, spacing: 16) {
                                        HStack(alignment: .top) {
                                            Image(systemName: "circle.fill")
                                                .foregroundColor(.green)
                                                .frame(width: 24, height: 24)

                                            VStack(alignment: .leading, spacing: 8) {
                                                Text("Hi! You can send your message or get inspired from suggestions. 😊")
                                                    .foregroundColor(.white)

                                                HStack(spacing: 8) {
                                                    QuickActionButton(title: "Suggestions", icon: "lightbulb") {
                                                        messageText = "Give me some creative writing prompts"
                                                        sendMessage()
                                                    }

                                                    QuickActionButton(title: "Elite Tools", icon: "gear") {
                                                        messageText = "What elite tools are available in this app?"
                                                        sendMessage()
                                                    }
                                                }

                                                HStack(spacing: 8) {
                                                    QuickActionButton(title: "AI Image Generator", icon: "photo") {
                                                        messageText = "How can I generate AI images with this app?"
                                                        sendMessage()
                                                    }

                                                    QuickActionButton(title: "Assistants", icon: "person.fill") {
                                                        messageText = "What assistants are available in this app?"
                                                        sendMessage()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    .padding()
                                }
                            } else {
                                // Regular chat messages
                                LazyVStack(spacing: 8) {
                                    ForEach(messages) { message in
                                        ChatScreenMessageBubble(message: message, assistant: assistant)
                                            .id(message.id)
                                    }

                                    // Show ChatGPT-style typing indicator when streaming
                                    if isStreaming {
                                        AIThinkingBubble()
                                            .id("typing-indicator")
                                    }
                                }
                                .id(refreshTrigger) // Force refresh when refreshTrigger changes
                            }
                        }
                        .padding(.top)
                        .onChange(of: messages.count) { _ in
                            // Auto-scroll to bottom when new messages are added
                            if let lastMessage = messages.last {
                                withAnimation(.easeOut(duration: 0.2)) {
                                    proxy.scrollTo(lastMessage.id, anchor: .bottom)
                                }
                            }
                        }
                        .onChange(of: currentStreamingMessage?.content) { _ in
                            // Auto-scroll during streaming
                            if isStreaming, let lastMessage = messages.last {
                                proxy.scrollTo(lastMessage.id, anchor: .bottom)
                            }
                        }
                    }
                }



                // Input area
                ChatInputView(messageText: $messageText, onSend: {
                    sendMessage()
                }, disableNavigation: true)
                .focused($isInputFieldFocused)
            }

            // Model selection dropdown
            if showModelSelection {
                VStack {
                    ModelSelectionView(
                        isShowing: $showModelSelection,
                        selectedModel: $selectedModel
                    )
                    Spacer()
                }
                .background(
                    Color.black.opacity(0.5)
                        .edgesIgnoringSafeArea(.all)
                        .onTapGesture {
                            showModelSelection = false
                        }
                )
                .zIndex(1)
            }

            // Examples popup
            if showExamplesPopup, let category = categoryName {
                ExamplesPopupView(
                    categoryName: category,
                    examples: categoryExamples,
                    onExampleSelected: { example in
                        messageText = example
                        showExamplesPopup = false
                        sendMessage()
                    },
                    onCustomPrompt: {
                        showExamplesPopup = false
                        // Focus on the input field for custom prompt
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            isInputFieldFocused = true
                        }
                    },
                    onDismiss: {
                        showExamplesPopup = false
                    }
                )
                .zIndex(2)
            }

            // Assistant intro popup
            if showAssistantIntro, let assistant = assistant {
                AssistantIntroPopupView(
                    assistant: assistant,
                    onContinue: {
                        showAssistantIntro = false
                    },
                    onDismiss: {
                        showAssistantIntro = false
                    },
                    onPromptSelected: { promptText in
                        messageText = promptText
                        showAssistantIntro = false
                        // Auto-send the selected prompt
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            sendMessage()
                        }
                    }
                )
                .zIndex(3)
            }

            // Options dropdown
            if showOptionsDropdown {
                ChatOptionsDropdownView(
                    onHistoryTapped: {
                        showChatHistory = true
                        showOptionsDropdown = false
                    },
                    onAutoReadToggled: {
                        // Handle auto-read toggle
                        print("Auto-read toggled")
                    },
                    onFontSizeTapped: {
                        // Handle font size action
                        print("Font size tapped")
                    },
                    onDismiss: {
                        showOptionsDropdown = false
                    }
                )
                .zIndex(4)
            }
        }
        .background(Color.black)
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .toolbar {
            ChatToolbarContent(
                showModelSelection: $showModelSelection,
                selectedModel: selectedModel,
                dismissAction: { dismiss() },
                categoryName: categoryName,
                categoryIcon: categoryIcon,
                assistant: assistant,
                onInfoButtonTapped: {
                    // Force show the assistant intro popup when info button is tapped
                    if assistant != nil {
                        forceShowAssistantIntro = true
                        showAssistantIntro = true
                    }
                },
                onNewChatTapped: {
                    // Start a new chat with the same assistant/category
                    startNewChat()
                },
                onMoreOptionsTapped: {
                    // Show options dropdown
                    showOptionsDropdown = true
                }
            )
        }
        .onAppear {
            // Set up streaming delegate
            print("ChatScreenView: Setting streaming delegate")
            ChatService.shared.streamingDelegate = self
            print("ChatScreenView: Delegate set to: \(ChatService.shared.streamingDelegate != nil ? "SET" : "NIL")")

            // Show assistant intro popup if this is an assistant chat and first visit
            if let assistant = assistant {
                let hasVisited = UserDefaults.standard.hasVisitedAssistant(assistant.name)
                if !hasVisited || forceShowAssistantIntro {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        showAssistantIntro = true
                        // Mark as visited when showing for the first time
                        if !hasVisited {
                            UserDefaults.standard.markAssistantAsVisited(assistant.name)
                        }
                        // Reset force flag
                        forceShowAssistantIntro = false
                    }
                }
            }

            // Auto-focus the input field when the view appears
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                isInputFieldFocused = true
            }

            // Auto-send the initial message if needed
            if shouldAutoSend, let initialMessage = initialMessageToSend, !didSendMessage {
                messageText = initialMessage
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    sendMessage()
                }
            }
        }
        .fullScreenCover(isPresented: $showImageResults) {
            if let imageURL = selectedImageURL {
                ImageResultsView(imageURL: imageURL, isPresented: $showImageResults)
            }
        }
        .fullScreenCover(isPresented: $showChatHistory) {
            ChatHistoryView()
        }
    }

    // MARK: - New Chat Functionality
    private func startNewChat() {
        // Clear current messages and reset state
        messages.removeAll()
        messageText = ""
        chatTitle = ""
        isStreaming = false
        isLoading = false
        currentStreamingMessage = nil
        didSendMessage = false
        currentChatId = nil // Reset chat ID for new conversation
        refreshTrigger += 1

        // Focus on input field for new conversation
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            isInputFieldFocused = true
        }
    }

    // MARK: - OPTIMIZED ChatStreamingDelegate for Real-time Streaming
    nonisolated func didReceiveStreamingToken(_ token: String) {
        Task { @MainActor in
            print("🔥 ChatScreenView RECEIVED TOKEN: '\(token)'")

            // Update the last AI message with streaming content
            if !messages.isEmpty && !messages.last!.isUser {
                let lastIndex = messages.count - 1
                let currentContent = messages[lastIndex].content
                let newContent = currentContent + token

                // Create new message with updated content
                let newMessage = Message(
                    content: newContent,
                    isUser: false,
                    timestamp: Date()
                )

                // Replace the last message
                messages[lastIndex] = newMessage
                currentStreamingMessage = newMessage

                print("🔥 ChatScreenView UPDATED MESSAGE: '\(newContent)'")

                // Trigger UI update
                refreshTrigger += 1
            }
        }
    }

    nonisolated func didCompleteStreaming() {
        Task { @MainActor in
            isStreaming = false
            currentStreamingMessage = nil
            isLoading = false

            // Save chat history to CoreData
            ChatHistory.saveChat(title: chatTitle, messages: messages, context: viewContext)
        }
    }

    nonisolated func didFailStreaming(with error: Error) {
        Task { @MainActor in
            isStreaming = false
            isLoading = false
            if let currentMessage = currentStreamingMessage,
               let index = messages.firstIndex(where: { $0.id == currentMessage.id }) {
                messages[index].content = "Sorry, I encountered an error: \(error.localizedDescription)"
            }
            currentStreamingMessage = nil
            errorMessage = error.localizedDescription

            // Still save chat history even if there was an error
            ChatHistory.saveChat(title: chatTitle, messages: messages, context: viewContext)
        }
    }

    private func sendMessage() {
        guard !messageText.isEmpty && !isStreaming else {
            print("🔥 ChatScreenView BLOCKED: messageText='\(messageText)', isStreaming=\(isStreaming)")
            return
        }

        print("🔥 ChatScreenView SENDING MESSAGE: '\(messageText)'")

        let userMessageContent = messageText

        // Add user message to UI immediately
        let userMessage = Message(content: userMessageContent, isUser: true, timestamp: Date())
        messages.append(userMessage)
        print("🔥 ChatScreenView ADDED USER MESSAGE, total messages: \(messages.count)")

        // Mark that we've sent a message (for history tracking)
        didSendMessage = true

        // Set chat title based on first user message if not already set
        if chatTitle.isEmpty {
            // Use the first few words of the message as the title
            let words = userMessageContent.split(separator: " ")
            if words.count > 3 {
                chatTitle = words.prefix(3).joined(separator: " ") + "..."
            } else {
                chatTitle = userMessageContent
            }
        }

        // Clear input
        messageText = ""

        // Set loading state
        isLoading = true
        isStreaming = true

        // Check if this is an AI Image Generator request
        if categoryName == "AI Image Generator" {
            handleImageGeneration(userMessage: userMessage)
        } else {
            // Handle regular chat with backend
            handleBackendChat(userMessageContent: userMessageContent)
        }
    }

    private func handleImageGeneration(userMessage: Message) {
        // Create empty AI message for image generation
        let aiMessage = Message(content: "🎨 Generating your image...", isUser: false, timestamp: Date())
        messages.append(aiMessage)
        currentStreamingMessage = aiMessage
        isLoading = true

        Task {
            do {
                let imageURL: URL

                if let selectedImage = selectedImageForGeneration {
                    // Edit existing image
                    print("🖼️ Editing image with prompt: \(userMessage.content)")
                    imageURL = try await ImageGenerationService.shared.editImage(
                        image: selectedImage,
                        prompt: userMessage.content,
                        model: "dall-e-2",
                        size: "1024x1024"
                    )
                } else {
                    // Generate new image
                    print("🖼️ Generating new image with prompt: \(userMessage.content)")
                    imageURL = try await ImageGenerationService.shared.generateImage(
                        prompt: userMessage.content,
                        model: "gpt-image-1",
                        size: "1024x1024"
                    )
                }

                // Update the AI message with the generated image
                DispatchQueue.main.async {
                    if let index = self.messages.firstIndex(where: { $0.id == aiMessage.id }) {
                        self.messages[index].content = "Here's your generated image:"
                        self.messages[index].imageURL = imageURL
                    }
                    self.isLoading = false
                    self.currentStreamingMessage = nil

                    // Save chat history
                    ChatHistory.saveChat(title: self.chatTitle, messages: self.messages, context: self.viewContext)
                }

            } catch {
                // Handle error
                DispatchQueue.main.async {
                    if let index = self.messages.firstIndex(where: { $0.id == aiMessage.id }) {
                        self.messages[index].content = "Sorry, I couldn't generate the image: \(error.localizedDescription)"
                    }
                    self.isLoading = false
                    self.currentStreamingMessage = nil
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }

    private func handleBackendChat(userMessageContent: String) {
        Task {
            do {
                // Create or get chat ID
                if currentChatId == nil {
                    print("🔥 ChatScreenView: Creating new chat")
                    currentChatId = try await chatHistoryService.createChat(
                        assistantId: selectedModel.name,
                        title: chatTitle
                    )
                    print("🔥 ChatScreenView: Created chat with ID: \(currentChatId ?? "nil")")
                }

                guard let chatId = currentChatId else {
                    throw ChatService.ChatServiceError.invalidResponse
                }

                // Send message to backend and get AI response
                print("🔥 ChatScreenView: Sending message to backend")
                let aiResponse = try await chatHistoryService.sendMessage(
                    chatId: chatId,
                    message: userMessageContent
                )

                // Add AI response to UI
                await MainActor.run {
                    let aiMessage = Message(content: aiResponse, isUser: false, timestamp: Date())
                    messages.append(aiMessage)
                    isLoading = false
                    isStreaming = false
                    currentStreamingMessage = nil
                    print("🔥 ChatScreenView: Added AI response, total messages: \(messages.count)")
                }

            } catch {
                print("🔥 ChatScreenView: Error sending message: \(error)")
                await MainActor.run {
                    let errorMsg = Message(
                        content: "Sorry, I encountered an error: \(error.localizedDescription)",
                        isUser: false,
                        timestamp: Date()
                    )
                    messages.append(errorMsg)
                    isLoading = false
                    isStreaming = false
                    currentStreamingMessage = nil
                    errorMessage = error.localizedDescription
                }
            }
        }
    }
}

#Preview {
    NavigationView {
        ChatScreenView(initialMessage: "Hello, how can you help me?", autoSend: true, assistant: nil)
    }
    .preferredColorScheme(.dark)
}

// MARK: - Additional Initializers
extension ChatScreenView {
    // Initialize with a document URL
    init(suggestion: Suggestion, autoSend: Bool = false, documentURL: URL? = nil, assistant: Assistant? = nil) {
        // Initialize with the suggestion first
        self.init(suggestion: suggestion, autoSend: autoSend, showExamplesOnAppear: false, assistant: assistant)

        // Note: State modification after init is not supported in SwiftUI
        // Document handling should be done in onAppear or through other means
        if let documentURL = documentURL {
            print("ChatScreenView initialized with document: \(documentURL.path)")
        }
    }

    // Initialize with a web URL
    init(suggestion: Suggestion, autoSend: Bool = false, webURL: URL? = nil, assistant: Assistant? = nil) {
        // Initialize with the suggestion first
        self.init(suggestion: suggestion, autoSend: autoSend, showExamplesOnAppear: false, assistant: assistant)

        // Note: State modification after init is not supported in SwiftUI
        // Web URL handling should be done in onAppear or through other means
        if let webURL = webURL {
            print("ChatScreenView initialized with webpage: \(webURL.absoluteString)")
        }
    }
}

// Modern Message Bubble for ChatScreenView
struct ChatScreenMessageBubble: View {
    let message: Message
    let assistant: Assistant?
    @State private var showActions = false
    @State private var isAnimating = false
    @State private var showImageResults = false
    @State private var selectedImageURL: URL?

    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            if message.isUser {
                Spacer(minLength: 50)
            } else {
                // AI Avatar - Show assistant image if available
                Group {
                    if let assistant = assistant, let uiImage = UIImage(named: assistant.name) {
                        // Show assistant image
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 32, height: 32)
                            .clipShape(Circle())
                            .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                    } else {
                        // Default AI avatar
                        Circle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [Color.blue, Color.purple]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 32, height: 32)
                            .overlay(
                                Image(systemName: "brain.head.profile")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                            )
                            .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                }
            }

            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 6) {
                // Message Content
                VStack(alignment: .leading, spacing: 8) {
                    // Text content
                    if !message.content.isEmpty {
                        Text(formatMessageContent(message.content))
                            .font(.system(size: 16, weight: .regular, design: .default))
                            .lineSpacing(4)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 20, style: .continuous)
                                    .fill(message.isUser ?
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ) :
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.gray.opacity(0.1), Color.gray.opacity(0.05)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20, style: .continuous)
                                            .stroke(message.isUser ? Color.clear : Color.gray.opacity(0.1), lineWidth: 1)
                                    )
                            )
                            .foregroundColor(message.isUser ? .white : .primary)
                            .shadow(
                                color: message.isUser ? Color.blue.opacity(0.2) : Color.black.opacity(0.05),
                                radius: message.isUser ? 8 : 4,
                                x: 0,
                                y: message.isUser ? 4 : 2
                            )
                    }

                    // Image content
                    if let imageURL = message.imageURL {
                        AsyncImage(url: imageURL) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxWidth: 250, maxHeight: 250)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                        } placeholder: {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.gray.opacity(0.2))
                                .frame(width: 250, height: 250)
                                .overlay(
                                    ProgressView()
                                        .scaleEffect(1.2)
                                )
                        }
                        .onTapGesture {
                            selectedImageURL = imageURL
                            showImageResults = true
                        }
                    }
                }

                // Action buttons for AI messages
                if !message.isUser && !message.content.isEmpty && showActions {
                    HStack(spacing: 12) {
                        ChatScreenActionButton(icon: "doc.on.doc", action: {
                            copyToClipboard(message.content)
                        })

                        ChatScreenActionButton(icon: "arrow.clockwise", action: {
                            regenerateResponse()
                        })

                        ChatScreenActionButton(icon: "hand.thumbsup", action: {
                            // Like action
                        })

                        ChatScreenActionButton(icon: "hand.thumbsdown", action: {
                            // Dislike action
                        })
                    }
                    .padding(.top, 8)
                    .padding(.leading, 16)
                    .transition(.opacity.combined(with: .scale(scale: 0.9)))
                }

                // Timestamp
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 4)
            }
            .onTapGesture {
                if !message.isUser {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        showActions.toggle()
                    }
                }
            }

            if !message.isUser {
                Spacer(minLength: 50)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 6)
        .scaleEffect(isAnimating ? 1.0 : 0.95)
        .opacity(isAnimating ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8).delay(0.1)) {
                isAnimating = true
            }
        }
        .onChange(of: message.content) { _ in
            // Subtle animation on content change for streaming
            withAnimation(.easeOut(duration: 0.1)) {
                // Trigger subtle visual feedback
            }
        }
        .fullScreenCover(isPresented: $showImageResults) {
            if let imageURL = selectedImageURL {
                ImageResultsView(imageURL: imageURL, isPresented: $showImageResults)
            }
        }
    }

    private func formatMessageContent(_ content: String) -> AttributedString {
        // Try to parse markdown first
        if #available(iOS 15.0, *) {
            if let markdownString = try? AttributedString(
                markdown: content,
                options: AttributedString.MarkdownParsingOptions(interpretedSyntax: .inlineOnlyPreservingWhitespace)
            ) {
                var attributedString = markdownString

                // Apply base formatting
                let fullRange = attributedString.startIndex..<attributedString.endIndex
                attributedString[fullRange].font = .system(size: 16, weight: .regular, design: .default)

                return attributedString
            }
        }

        // Fallback: manual markdown parsing for older iOS versions or if markdown parsing fails
        return parseMarkdownManually(content)
    }

    private func parseMarkdownManually(_ content: String) -> AttributedString {
        var attributedString = AttributedString()
        let lines = content.components(separatedBy: .newlines)

        for (index, line) in lines.enumerated() {
            var lineAttributedString = AttributedString()

            // Handle headers
            if line.hasPrefix("### ") {
                let headerText = String(line.dropFirst(4))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 18, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("## ") {
                let headerText = String(line.dropFirst(3))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 20, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("# ") {
                let headerText = String(line.dropFirst(2))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 22, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("- ") || line.hasPrefix("* ") {
                // Handle bullet points
                let bulletText = String(line.dropFirst(2))
                var bulletAttr = AttributedString("• " + bulletText)
                bulletAttr.font = .system(size: 16, weight: .regular, design: .default)
                lineAttributedString.append(bulletAttr)
            } else {
                // Regular text with bold formatting
                var processedLine = line
                var lineAttr = AttributedString()

                // Simple bold text parsing
                while let boldRange = processedLine.range(of: #"\*\*([^*]+)\*\*"#, options: .regularExpression) {
                    // Add text before bold
                    let beforeBold = String(processedLine[..<boldRange.lowerBound])
                    if !beforeBold.isEmpty {
                        var beforeAttr = AttributedString(beforeBold)
                        beforeAttr.font = .system(size: 16, weight: .regular, design: .default)
                        lineAttr.append(beforeAttr)
                    }

                    // Add bold text
                    let boldText = String(processedLine[boldRange])
                    let cleanBoldText = boldText.replacingOccurrences(of: "**", with: "")
                    var boldAttr = AttributedString(cleanBoldText)
                    boldAttr.font = .system(size: 16, weight: .bold, design: .default)
                    lineAttr.append(boldAttr)

                    // Remove processed part
                    processedLine = String(processedLine[boldRange.upperBound...])
                }

                // Add remaining text
                if !processedLine.isEmpty {
                    var remainingAttr = AttributedString(processedLine)
                    remainingAttr.font = .system(size: 16, weight: .regular, design: .default)
                    lineAttr.append(remainingAttr)
                }

                lineAttributedString = lineAttr
            }

            // Add the line to the main attributed string
            attributedString.append(lineAttributedString)

            // Add line break if not the last line
            if index < lines.count - 1 {
                attributedString.append(AttributedString("\n"))
            }
        }

        return attributedString
    }

    private func copyToClipboard(_ text: String) {
        UIPasteboard.general.string = text
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    private func regenerateResponse() {
        // Implement regenerate functionality
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// Action Button for ChatScreenView
struct ChatScreenActionButton: View {
    let icon: String
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                isPressed = true
            }
            action()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                    isPressed = false
                }
            }
        }) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                )
                .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
}


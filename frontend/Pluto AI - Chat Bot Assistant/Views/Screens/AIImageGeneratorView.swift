//
//  AIImageGeneratorView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.05.01.
//

import SwiftUI

struct AIImageGeneratorView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var promptText = ""
    @State private var selectedImage: UIImage? = nil
    @State private var isImagePickerPresented = false
    @State private var navigateToChat = false
    @State private var showSimplifiedInput = false
    @State private var generationMode: ImageGenerationMode = .textToImage

    enum ImageGenerationMode {
        case textToImage
        case imageToImage
    }

    var body: some View {
        // Full screen dark background
        ZStack {
            // Dark background
            Color.black
                .edgesIgnoringSafeArea(.all)

            VStack(spacing: 0) {
                Spacer()

                // Main content area
                VStack(spacing: 0) {
                    // Drag indicator
                    Capsule()
                        .fill(Color.gray.opacity(0.5))
                        .frame(width: 40, height: 5)
                        .padding(.bottom, 30)

                    // Header with sample images and title
                    HStack(spacing: 12) {
                        // Sample images overlapping
                        ZStack {
                            Image("artist_assistant") // Use actual asset image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 50, height: 50)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                                .offset(x: -10)

                            Image("artist_assistant") // Use actual asset image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 50, height: 50)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                                .offset(x: 10)
                        }

                        Text("AI Image Generator")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Spacer()
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)

                    // Description
                    Text("Your conversations come alive through automatically generated visuals. Type in your message and Chat & Ask AI.")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.leading)
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)

                    // Show selected image or add image button
                    if let selectedImage = selectedImage {
                        // Show selected image with remove option
                        VStack(spacing: 12) {
                            HStack {
                                Text("Selected Image")
                                    .font(.subheadline)
                                    .foregroundColor(.white)
                                Spacer()
                                Button("Remove") {
                                    self.selectedImage = nil
                                    self.generationMode = .textToImage
                                }
                                .font(.caption)
                                .foregroundColor(.red)
                            }

                            // Display selected image
                            Image(uiImage: selectedImage)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(height: 120)
                                .clipped()
                                .cornerRadius(12)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    } else {
                        // Add image button (only show when no image selected)
                        Button(action: {
                            isImagePickerPresented = true
                        }) {
                            HStack(spacing: 12) {
                                Image(systemName: "photo")
                                    .font(.title2)
                                    .foregroundColor(.gray)

                                Text("Add Image (Optional)")
                                    .font(.body)
                                    .foregroundColor(.gray)

                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color(UIColor.systemGray6).opacity(0.3))
                            .cornerRadius(12)
                        }
                        .padding(.horizontal, 20)
                        .padding(.bottom, 30)
                    }

                    // Prompt text field
                    Button(action: {
                        showSimplifiedInput = true
                    }) {
                        HStack {
                            Text(promptText.isEmpty ?
                                 (selectedImage != nil ? "Describe how to edit this image..." : "What do you want to create today?") :
                                 promptText)
                                .font(.body)
                                .foregroundColor(promptText.isEmpty ? .gray : .white)
                                .multilineTextAlignment(.leading)
                            Spacer()
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(Color(UIColor.systemGray6).opacity(0.3))
                        .cornerRadius(12)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)

                    // Continue button
                    Button(action: {
                        navigateToChat = true
                    }) {
                        Text("Continue")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(Color.white)
                            .cornerRadius(12)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 40)
                }
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color(UIColor.systemGray6).opacity(0.15))
                )
                .padding(.horizontal, 0)
            }
        }
        .background(Color.black.edgesIgnoringSafeArea(.all))
        .navigationBarHidden(true)
        // Make the view dismissible by dragging down
        .interactiveDismissDisabled(false)
        .sheet(isPresented: $isImagePickerPresented) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .sheet(isPresented: $showSimplifiedInput) {
            // Show the simplified input view with a callback to receive the entered text
            AIImageInputView(
                selectedImage: selectedImage,
                generationMode: selectedImage != nil ? .imageToImage : .textToImage,
                onTextEntered: { newText in
                    // Update the prompt text with the text entered in the simplified view
                    promptText = newText
                    // If the user entered text and pressed Continue, navigate to chat
                    if !newText.isEmpty {
                        navigateToChat = true
                    }
                }
            )
        }
        .fullScreenCover(isPresented: $navigateToChat) {
            NavigationView {
                // Create a specialized chat view for image generation with a system message
                let imageGenPrompt = promptText.isEmpty ? "Generate an image of a beautiful landscape" : promptText
                let suggestionText = selectedImage != nil ?
                    "Edit this image: \(imageGenPrompt)" :
                    "Generate an image of: \(imageGenPrompt)"

                let imageGenSuggestion = Suggestion(
                    text: suggestionText,
                    icon: "🖼️",
                    category: "AI Image Generator",
                    examplePrompts: [
                        "Create a photorealistic image",
                        "Generate an artistic illustration",
                        "Make a creative digital artwork"
                    ]
                )

                // Use the image generation service in the backend
                ChatScreenView(
                    suggestion: imageGenSuggestion,
                    autoSend: true,
                    selectedImage: selectedImage
                )
            }
        }
    }
}

// Image Picker struct to handle image selection
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) private var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

#Preview {
    AIImageGeneratorView()
        .preferredColorScheme(.dark)
}

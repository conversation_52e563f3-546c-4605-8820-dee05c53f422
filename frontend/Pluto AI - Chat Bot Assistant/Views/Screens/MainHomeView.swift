//
//  MainHomeView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct MainHomeView: View {
    @State private var messageText = ""
    @State private var messageToSend = "" // Store the message to send
    @State private var selectedCategory = "E-Mail"
    @State private var showNewChat = false
    @State private var showAllAssistants = false
    @State private var showAllHistory = false
    @State private var showSettings = false
    @State private var showBackendTest = false
    @State private var debugTapCount = 0
    @State private var chatHistories: [ChatHistory] = []
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var assistantManager = AssistantManager.shared

    // Function to handle when a suggestion is tapped
    func handleSuggestionTapped(_ suggestion: Suggestion) {
        print("MainHomeView: Suggestion tapped: \(suggestion.text) from category \(suggestion.category)")

        // Clear any previously saved category data to ensure we start fresh
        UserDefaults.standard.removeObject(forKey: "lastSelectedCategory")
        UserDefaults.standard.removeObject(forKey: "lastSystemMessage")
        UserDefaults.standard.removeObject(forKey: "lastCategoryExamples")

        // We'll use the suggestion directly in the chat screen
        messageToSend = suggestion.text
        showNewChat = true

        // Store the suggestion for use in the chat screen
        selectedSuggestion = suggestion
    }

    // Store the selected suggestion to pass to the chat screen
    @State private var selectedSuggestion: Suggestion? = nil

    var body: some View {
        ZStack(alignment: .top) {
            // Main content
            ScrollView(.vertical, showsIndicators: true) {
                VStack(alignment: .leading, spacing: 8) {
                    // Add minimal padding at the top to account for the header
                    Spacer()
                        .frame(height: 20)
                    // Elite Tools Section
                    SectionHeaderView(title: "Elite Tools", showSeeAll: false)

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(alignment: .top, spacing: 1) {
                            ForEach(Tool.displayedEliteTools) { tool in
                                ToolCardView(tool: tool)
                            }
                        }
                        .padding(.horizontal)
                    }

                    // Assistants Section
                    SectionHeaderView(title: "Assistants", showSeeAll: true) {
                        // Navigate to all assistants
                        showAllAssistants = true
                    }

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 15) {
                            CreateAssistantCardView()

                            ForEach(assistantManager.allAssistants.prefix(3)) { assistant in
                                AssistantCardView(assistant: assistant)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .fullScreenCover(isPresented: $showAllAssistants) {
                        AssistantsView()
                    }

                    // My History Section
                    SectionHeaderView(title: "My History", showSeeAll: chatHistories.count > 3) {
                        // Navigate to all history
                        showAllHistory = true
                    }

                    if chatHistories.isEmpty {
                        Text("No chat history yet. Start a conversation!")
                            .foregroundColor(.gray)
                            .padding()
                    } else {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 15) {
                                ForEach(chatHistories.prefix(3)) { history in
                                    HistoryCardView(history: history)
                                        .frame(width: 200)
                                }
                            }
                            .padding(.horizontal)
                        }
                    }

                    // Suggestions Section
                    SectionHeaderView(title: "Suggestions", showSeeAll: false)

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 10) {
                            ForEach(SuggestionCategory.categories) { category in
                                CategoryPillView(category: category, selectedCategory: $selectedCategory)
                            }
                        }
                        .padding(.horizontal)
                    }

                    VStack(spacing: 10) {
                        if selectedCategory == "E-Mail" {
                            SuggestionsListView(suggestions: Suggestion.emailSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Business & Marketing" {
                            SuggestionsListView(suggestions: Suggestion.businessSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Astrology" {
                            SuggestionsListView(suggestions: Suggestion.astrologySuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Education" {
                            SuggestionsListView(suggestions: Suggestion.educationSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Art" {
                            SuggestionsListView(suggestions: Suggestion.artSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Travel" {
                            SuggestionsListView(suggestions: Suggestion.travelSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Daily Lifestyle" {
                            SuggestionsListView(suggestions: Suggestion.lifestyleSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Relationship" {
                            SuggestionsListView(suggestions: Suggestion.relationshipSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Fun" {
                            SuggestionsListView(suggestions: Suggestion.funSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Social" {
                            SuggestionsListView(suggestions: Suggestion.socialSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Career" {
                            SuggestionsListView(suggestions: Suggestion.careerSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Health & Nutrition" {
                            SuggestionsListView(suggestions: Suggestion.healthSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else if selectedCategory == "Greetings" {
                            SuggestionsListView(suggestions: Suggestion.greetingsSuggestions, onSuggestionTapped: handleSuggestionTapped)
                        } else {
                            // Placeholder for other categories
                            Text("No suggestions available for this category")
                                .foregroundColor(.gray)
                                .padding()
                        }
                    }
                    .padding(.horizontal)

                    Spacer(minLength: 20) // Minimal space for input field
                }
            }
            .background(Color.black)

            // Header overlay with high z-index to ensure it stays on top
            HomeToolbarView(showSettings: $showSettings)
                .zIndex(100) // High z-index to ensure it stays on top
        }
        .safeAreaInset(edge: .bottom, spacing: 0) {
                ChatInputView(
                    messageText: $messageText,
                    onSend: {
                        // This onSend callback will be called when the user taps the send button
                        // or when voice recognition completes
                        if !messageText.isEmpty {
                            // Make sure the message is not empty
                            let trimmedMessage = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
                            if !trimmedMessage.isEmpty {
                                print("MainHomeView: Storing message to send: \(trimmedMessage)")
                                messageToSend = trimmedMessage // Store the trimmed message
                                messageText = "" // Clear the input field immediately
                                showNewChat = true
                            }
                        }
                    },
                    onTapToChat: {
                        // Instant navigation to default chat when text field is tapped
                        print("MainHomeView: Text field tapped - opening default chat")
                        messageToSend = "" // No initial message
                        showNewChat = true
                    }
                )
            }
            // We don't need this sheet anymore as the ChatInputView now handles opening the chat screen
            // when the text field is tapped or when a voice recording is completed
            .fullScreenCover(isPresented: $showNewChat, onDismiss: {
                // Reset the stored message when the chat screen is dismissed
                messageToSend = ""
                // Don't reset the selectedSuggestion to nil as it would clear the category information
                // We'll let the UserDefaults persistence handle this
                // Reload chat histories to show the new chat
                chatHistories = ChatHistory.getAllChats(context: viewContext)
            }) {
                NavigationView {
                    // If we have a selected suggestion, use it to create a specialized chat
                    if let suggestion = selectedSuggestion {
                        ChatScreenView(suggestion: suggestion, autoSend: false, showExamplesOnAppear: false, assistant: nil)
                    } else if !messageToSend.isEmpty {
                        // If we have a message to send, auto-send it
                        ChatScreenView(initialMessage: messageToSend, autoSend: true, assistant: nil)
                    } else {
                        // Default chat with immediate focus for typing
                        DefaultChatView()
                    }
                }
                .preferredColorScheme(.dark)
            }
            .sheet(isPresented: $showSettings) {
                SettingsView()
            }
            .fullScreenCover(isPresented: $showAllHistory) {
                HistoryView()
            }
            .onAppear {
                // Load chat histories from CoreData
                chatHistories = ChatHistory.getAllChats(context: viewContext)
            }
        }
    }

#Preview {
    MainHomeView()
        .preferredColorScheme(.dark)
}

//
//  ChatHistoryView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.06.19.
//

import SwiftUI

struct ChatHistoryView: View {
    @StateObject private var chatHistoryService = ChatHistoryService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var selectedChat: BackendChat?
    @State private var showChatView = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                if chatHistoryService.isLoading {
                    VStack {
                        ProgressView()
                            .scaleEffect(1.2)
                            .tint(.white)
                        
                        Text("Loading chat history...")
                            .foregroundColor(.secondary)
                            .padding(.top)
                    }
                } else if chatHistoryService.chats.isEmpty {
                    VStack(spacing: 20) {
                        Image(systemName: "message.circle")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        
                        Text("No chat history yet")
                            .font(.title2)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        
                        Text("Start a conversation to see your chat history here")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        But<PERSON>("Start New Chat") {
                            dismiss()
                        }
                        .buttonStyle(.borderedProminent)
                        .tint(.blue)
                    }
                } else {
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(chatHistoryService.chats) { chat in
                                ChatHistoryCard(chat: chat) {
                                    selectedChat = chat
                                    showChatView = true
                                }
                            }
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle("Chat History")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(.blue)
                }
            }
        }
        .onAppear {
            Task {
                await chatHistoryService.loadChats()
            }
        }
        .fullScreenCover(isPresented: $showChatView) {
            if let chat = selectedChat {
                ChatHistoryDetailView(chat: chat)
            }
        }
    }
}

struct ChatHistoryCard: View {
    let chat: BackendChat
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(chat.title)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        Text(chat.assistantId)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.gray.opacity(0.2))
                            .cornerRadius(4)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text(chat.createdAt, style: .date)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(chat.messages.count) messages")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                if let lastMessage = chat.messages.last {
                    Text(lastMessage.content)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ChatHistoryDetailView: View {
    let chat: BackendChat
    @Environment(\.dismiss) private var dismiss
    @State private var messages: [Message] = []
    @State private var isLoading = true
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.black.ignoresSafeArea()
                
                if isLoading {
                    ProgressView()
                        .scaleEffect(1.2)
                        .tint(.white)
                } else {
                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach(messages) { message in
                                ChatScreenMessageBubble(message: message, assistant: nil)
                            }
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle(chat.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Back") {
                        dismiss()
                    }
                    .foregroundColor(.blue)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Continue") {
                        // TODO: Continue this chat in ChatScreenView
                        dismiss()
                    }
                    .foregroundColor(.blue)
                }
            }
        }
        .onAppear {
            loadChatMessages()
        }
    }
    
    private func loadChatMessages() {
        Task {
            do {
                let fullChat = try await ChatHistoryService.shared.loadChat(chatId: chat.id)
                await MainActor.run {
                    messages = ChatHistoryService.shared.convertToMessages(fullChat)
                    isLoading = false
                }
            } catch {
                print("Error loading chat messages: \(error)")
                await MainActor.run {
                    isLoading = false
                }
            }
        }
    }
}

#Preview {
    ChatHistoryView()
        .preferredColorScheme(.dark)
}

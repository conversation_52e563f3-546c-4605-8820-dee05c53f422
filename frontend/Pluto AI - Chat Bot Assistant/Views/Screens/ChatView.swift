//
//  ChatView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

// Streaming message bubble that can update its content progressively
struct StreamingMessageBubble: View {
    let message: Message
    @State private var displayedContent: String = ""
    @State private var isTyping: Bool = false

    var body: some View {
        HStack {
            if message.isUser {
                Spacer()
            }

            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 4) {
                HStack {
                    Text(displayedContent)
                        .padding(12)
                        .background(message.isUser ? Color.blue : Color.systemGray5)
                        .foregroundColor(.white)
                        .cornerRadius(16)

                    // Show typing indicator for AI messages
                    if !message.isUser && isTyping {
                        TypingIndicator()
                    }
                }

                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.gray)
            }

            if !message.isUser {
                Spacer()
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 4)
        .onAppear {
            displayedContent = message.content
        }
        .onChange(of: message.content) { newContent in
            displayedContent = newContent
        }
    }

    func updateContent(_ newContent: String) {
        displayedContent = newContent
    }

    func setTyping(_ typing: Bool) {
        isTyping = typing
    }
}

// Typing indicator with blinking cursor
struct TypingIndicator: View {
    @State private var isBlinking = false

    var body: some View {
        Text("▋")
            .foregroundColor(.white)
            .opacity(isBlinking ? 0.3 : 1.0)
            .animation(.easeInOut(duration: 0.8).repeatForever(autoreverses: true), value: isBlinking)
            .onAppear {
                isBlinking = true
            }
    }
}

struct MessageBubble: View {
    let message: Message
    @State private var showActions = false
    @State private var isAnimating = false
    @State private var showImageResults = false
    @State private var selectedImageURL: URL?

    var body: some View {
        HStack(alignment: .bottom, spacing: 8) {
            if message.isUser {
                Spacer(minLength: 50)
            } else {
                // AI Avatar
                Circle()
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [Color.blue, Color.purple]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "brain.head.profile")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    )
                    .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
            }

            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 6) {
                // Message Content
                VStack(alignment: .leading, spacing: 8) {
                    // Text content
                    if !message.content.isEmpty {
                        Text(formatMessageContent(message.content))
                            .font(.system(size: 16, weight: .regular, design: .default))
                            .lineSpacing(4)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 20, style: .continuous)
                                    .fill(message.isUser ?
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ) :
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.gray.opacity(0.1), Color.gray.opacity(0.05)]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 20, style: .continuous)
                                            .stroke(message.isUser ? Color.clear : Color.gray.opacity(0.1), lineWidth: 1)
                                    )
                            )
                            .foregroundColor(message.isUser ? .white : .primary)
                            .shadow(
                                color: message.isUser ? Color.blue.opacity(0.2) : Color.black.opacity(0.05),
                                radius: message.isUser ? 8 : 4,
                                x: 0,
                                y: message.isUser ? 4 : 2
                            )
                    }

                    // Image content
                    if let imageURL = message.imageURL {
                        AsyncImage(url: imageURL) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxWidth: 250, maxHeight: 250)
                                .cornerRadius(12)
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                        } placeholder: {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.gray.opacity(0.2))
                                .frame(width: 250, height: 250)
                                .overlay(
                                    ProgressView()
                                        .scaleEffect(1.2)
                                )
                        }
                        .onTapGesture {
                            selectedImageURL = imageURL
                            showImageResults = true
                        }
                    }
                }

                // Action buttons for AI messages
                if !message.isUser && !message.content.isEmpty && showActions {
                    HStack(spacing: 12) {
                        ActionButton(icon: "doc.on.doc", action: {
                            copyToClipboard(message.content)
                        })

                        ActionButton(icon: "arrow.clockwise", action: {
                            regenerateResponse()
                        })

                        ActionButton(icon: "hand.thumbsup", action: {
                            // Like action
                        })

                        ActionButton(icon: "hand.thumbsdown", action: {
                            // Dislike action
                        })
                    }
                    .padding(.top, 8)
                    .padding(.leading, 16)
                    .transition(.opacity.combined(with: .scale(scale: 0.9)))
                }

                // Timestamp
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 4)
            }
            .onTapGesture {
                if !message.isUser {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        showActions.toggle()
                    }
                }
            }

            if !message.isUser {
                Spacer(minLength: 50)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 6)
        .scaleEffect(isAnimating ? 1.0 : 0.95)
        .opacity(isAnimating ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.spring(response: 0.4, dampingFraction: 0.8).delay(0.1)) {
                isAnimating = true
            }
        }
        .onChange(of: message.content) { _ in
            // Subtle animation on content change for streaming
            withAnimation(.easeOut(duration: 0.1)) {
                // Trigger subtle visual feedback
            }
        }
        .fullScreenCover(isPresented: $showImageResults) {
            if let imageURL = selectedImageURL {
                ImageResultsView(imageURL: imageURL, isPresented: $showImageResults)
            }
        }
    }

    private func formatMessageContent(_ content: String) -> AttributedString {
        // Try to parse markdown first
        if #available(iOS 15.0, *) {
            if let markdownString = try? AttributedString(
                markdown: content,
                options: AttributedString.MarkdownParsingOptions(interpretedSyntax: .inlineOnlyPreservingWhitespace)
            ) {
                var attributedString = markdownString

                // Apply base formatting
                let fullRange = attributedString.startIndex..<attributedString.endIndex
                attributedString[fullRange].font = .system(size: 16, weight: .regular, design: .default)

                return attributedString
            }
        }

        // Fallback: manual markdown parsing for older iOS versions or if markdown parsing fails
        return parseMarkdownManually(content)
    }

    private func parseMarkdownManually(_ content: String) -> AttributedString {
        var attributedString = AttributedString()
        let lines = content.components(separatedBy: .newlines)

        for (index, line) in lines.enumerated() {
            var lineAttributedString = AttributedString()

            // Handle headers
            if line.hasPrefix("### ") {
                let headerText = String(line.dropFirst(4))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 18, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("## ") {
                let headerText = String(line.dropFirst(3))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 20, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("# ") {
                let headerText = String(line.dropFirst(2))
                var headerAttr = AttributedString(headerText)
                headerAttr.font = .system(size: 22, weight: .bold, design: .default)
                lineAttributedString.append(headerAttr)
            } else if line.hasPrefix("- ") || line.hasPrefix("* ") {
                // Handle bullet points
                let bulletText = String(line.dropFirst(2))
                var bulletAttr = AttributedString("• " + bulletText)
                bulletAttr.font = .system(size: 16, weight: .regular, design: .default)
                lineAttributedString.append(bulletAttr)
            } else {
                // Regular text with bold formatting
                var processedLine = line
                var lineAttr = AttributedString()

                // Simple bold text parsing
                while let boldRange = processedLine.range(of: #"\*\*([^*]+)\*\*"#, options: .regularExpression) {
                    // Add text before bold
                    let beforeBold = String(processedLine[..<boldRange.lowerBound])
                    if !beforeBold.isEmpty {
                        var beforeAttr = AttributedString(beforeBold)
                        beforeAttr.font = .system(size: 16, weight: .regular, design: .default)
                        lineAttr.append(beforeAttr)
                    }

                    // Add bold text
                    let boldText = String(processedLine[boldRange])
                    let cleanBoldText = boldText.replacingOccurrences(of: "**", with: "")
                    var boldAttr = AttributedString(cleanBoldText)
                    boldAttr.font = .system(size: 16, weight: .bold, design: .default)
                    lineAttr.append(boldAttr)

                    // Remove processed part
                    processedLine = String(processedLine[boldRange.upperBound...])
                }

                // Add remaining text
                if !processedLine.isEmpty {
                    var remainingAttr = AttributedString(processedLine)
                    remainingAttr.font = .system(size: 16, weight: .regular, design: .default)
                    lineAttr.append(remainingAttr)
                }

                lineAttributedString = lineAttr
            }

            // Add the line to the main attributed string
            attributedString.append(lineAttributedString)

            // Add line break if not the last line
            if index < lines.count - 1 {
                attributedString.append(AttributedString("\n"))
            }
        }

        return attributedString
    }

    private func copyToClipboard(_ text: String) {
        UIPasteboard.general.string = text
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    private func regenerateResponse() {
        // Implement regenerate functionality
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
}

// Action Button Component for message interactions
struct ActionButton: View {
    let icon: String
    let action: () -> Void
    @State private var isPressed = false

    var body: some View {
        Button(action: {
            withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                isPressed = true
            }
            action()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                    isPressed = false
                }
            }
        }) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(Color.gray.opacity(0.1))
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                        )
                )
                .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }
}



@MainActor
struct ChatView: View, ChatStreamingDelegate {
    let history: ChatHistory
    @State private var messageText = ""
    @State private var messages: [Message] = []
    @State private var isStreaming = false
    @State private var currentStreamingMessage: Message?
    @State private var refreshTrigger = 0 // Force UI refresh like DefaultChatView

    // Backend chat management
    @StateObject private var chatHistoryService = ChatHistoryService.shared
    @State private var isLoading = false

    init(history: ChatHistory) {
        self.history = history
        // Use _messages to set the initial value of the @State property
        if !history.messages.isEmpty {
            _messages = State(initialValue: history.messages)
        }
    }

    // MARK: - OPTIMIZED ChatStreamingDelegate for Real-time Streaming
    nonisolated func didReceiveStreamingToken(_ token: String) {
        Task { @MainActor in
            print("🔥 ChatView RECEIVED TOKEN: '\(token)'")

            // Update the last AI message with streaming content
            if !messages.isEmpty && !messages.last!.isUser {
                let lastIndex = messages.count - 1
                let currentContent = messages[lastIndex].content
                let newContent = currentContent + token

                // Create new message with updated content
                let newMessage = Message(
                    content: newContent,
                    isUser: false,
                    timestamp: Date()
                )

                // Replace the last message
                messages[lastIndex] = newMessage
                currentStreamingMessage = newMessage

                print("🔥 ChatView UPDATED MESSAGE: '\(newContent)'")

                // Trigger UI update
                refreshTrigger += 1
            }
        }
    }

    nonisolated func didCompleteStreaming() {
        Task { @MainActor in
            isStreaming = false
            currentStreamingMessage = nil
        }
    }

    nonisolated func didFailStreaming(with error: Error) {
        Task { @MainActor in
            isStreaming = false
            if let currentMessage = currentStreamingMessage,
               let index = messages.firstIndex(where: { $0.id == currentMessage.id }) {
                messages[index].content = "Sorry, I encountered an error: \(error.localizedDescription)"
            }
            currentStreamingMessage = nil
        }
    }

    var body: some View {
        VStack {
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(messages) { message in
                            MessageBubble(message: message)
                                .id(message.id)
                        }

                        // Show ChatGPT-style typing indicator when streaming
                        if isStreaming {
                            AIThinkingBubble()
                                .id("typing-indicator")
                        }
                    }
                    .id(refreshTrigger) // Force refresh when refreshTrigger changes
                    .padding(.vertical, 16)
                }
                .onChange(of: messages.count) { _ in
                    // Auto-scroll to bottom when new messages are added (optimized)
                    if let lastMessage = messages.last {
                        withAnimation(.easeOut(duration: 0.2)) {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
                .onChange(of: currentStreamingMessage?.content) { _ in
                    // Auto-scroll during streaming for better UX (throttled)
                    if isStreaming, let lastMessage = messages.last {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }

            ChatInputView(messageText: $messageText, onSend: {
                sendMessage()
            }, disableNavigation: true)
        }
        .background(Color.black)
        .navigationTitle(history.title)
        #if os(iOS)
        .navigationBarTitleDisplayMode(.inline)
        #endif
        .onAppear {
            // Set up streaming delegate
            ChatService.shared.streamingDelegate = self
        }
    }

    private func sendMessage() {
        guard !messageText.isEmpty && !isStreaming else {
            print("🔥 ChatView BLOCKED: messageText='\(messageText)', isStreaming=\(isStreaming)")
            return
        }

        print("🔥 ChatView SENDING MESSAGE: '\(messageText)'")

        // Add user message
        let userMessage = Message(content: messageText, isUser: true, timestamp: Date())
        messages.append(userMessage)
        print("🔥 ChatView ADDED USER MESSAGE, total messages: \(messages.count)")

        let userMessageContent = messageText

        // Clear input
        messageText = ""

        // Set loading state
        isLoading = true
        isStreaming = true

        // Create empty AI message for streaming
        let aiMessage = Message(content: "", isUser: false, timestamp: Date())
        messages.append(aiMessage)
        currentStreamingMessage = aiMessage

        // Handle backend chat with proper saving
        handleBackendChat(userMessageContent: userMessageContent)
    }

    private func handleBackendChat(userMessageContent: String) {
        Task {
            do {
                // Use the chat ID from history (convert UUID to String)
                let chatId = history.id.uuidString

                // Send message to backend and get AI response
                print("🔥 ChatView: Sending message to backend chat: \(chatId)")
                let aiResponse = try await chatHistoryService.sendMessage(
                    chatId: chatId,
                    message: userMessageContent
                )

                // Update the AI message with the response
                await MainActor.run {
                    if let lastIndex = messages.lastIndex(where: { !$0.isUser }) {
                        messages[lastIndex] = Message(
                            content: aiResponse,
                            isUser: false,
                            timestamp: Date()
                        )
                    }

                    isLoading = false
                    isStreaming = false
                    currentStreamingMessage = nil
                    refreshTrigger += 1
                }

                print("🔥 ChatView: Backend response received and saved")

            } catch {
                print("🔥 ChatView: Error in backend chat: \(error)")
                await MainActor.run {
                    // Update AI message with error
                    if let lastIndex = messages.lastIndex(where: { !$0.isUser }) {
                        messages[lastIndex] = Message(
                            content: "Sorry, I encountered an error: \(error.localizedDescription)",
                            isUser: false,
                            timestamp: Date()
                        )
                    }

                    isLoading = false
                    isStreaming = false
                    currentStreamingMessage = nil
                    refreshTrigger += 1
                }
            }
        }
    }
}

#Preview {
    NavigationView {
        ChatView(history: ChatHistory(title: "Sample Chat", date: Date()))
    }
    .preferredColorScheme(.dark)
}

//
//  ExploreToolCardView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.07.09.
//

import SwiftUI

struct ExploreToolCardView: View {
    let tool: Tool
    @State private var showChatScreen = false

    var body: some View {
        Button(action: {
            showChatScreen = true
        }) {
            VStack(spacing: 8) {
                // Tool icon with colored border like reference
                ZStack {
                    // Black background container
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.black)
                        .frame(width: 80, height: 80)
                    
                    // Colored border
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(tool.iconBackgroundColor, lineWidth: 2)
                        .frame(width: 80, height: 80)
                    
                    // Icon content
                    if let assetImageName = tool.assetImageName {
                        Image(assetImageName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 50, height: 50)
                    } else {
                        Image(systemName: tool.icon)
                            .font(.system(size: 30, weight: .medium))
                            .foregroundColor(.white)
                    }
                    
                    // PRO badge if new
                    if tool.isNew {
                        VStack {
                            HStack {
                                Spacer()
                                HStack(spacing: 2) {
                                    Image(systemName: "diamond.fill")
                                        .font(.system(size: 8))
                                        .foregroundColor(.orange)
                                    Text("PRO")
                                        .font(.system(size: 8, weight: .bold))
                                        .foregroundColor(.white)
                                }
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.black.opacity(0.8))
                                )
                            }
                            Spacer()
                        }
                        .frame(width: 80, height: 80)
                    }
                }
                
                // Tool name
                Text(tool.name)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
            }
            .frame(width: 100, height: 120)
        }
        .buttonStyle(PlainButtonStyle())
        // Use sheet for AI Image Generator and Voice Chat
        .sheet(isPresented: $showChatScreen, content: {
            if tool.name == "AI Image Generator" {
                AIImageGeneratorView()
            } else if tool.name == "Voice Chat" {
                VoiceChatView()
            } else if tool.name == "AI Vision" {
                // Create a specialized AI Vision view
                BrowsingChatPopupView() // Placeholder for now
            } else if tool.name == "Browsing Chat" {
                BrowsingChatPopupView()
            } else if tool.name == "YouTube Summary" {
                YouTubeSummaryPopupView()
            } else if tool.name == "Upload & Ask" {
                UploadAndAskPopupView()
            } else if tool.name == "Link & Ask" {
                LinkAndAskPopupView()
            } else {
                // Default chat view for other tools
                NavigationView {
                    let toolSuggestion = Suggestion(
                        text: "How can I use the \(tool.name)?",
                        icon: tool.icon,
                        category: tool.name,
                        examplePrompts: [
                            "What are the main features of \(tool.name)?",
                            "How can \(tool.name) help me with my tasks?",
                            "Show me examples of what \(tool.name) can do"
                        ]
                    )
                    
                    ChatScreenView(suggestion: toolSuggestion, autoSend: false, showExamplesOnAppear: false, assistant: nil)
                }
            }
        })
    }
}

#Preview {
    ExploreToolCardView(tool: Tool.displayedEliteTools[0])
        .preferredColorScheme(.dark)
        .background(Color.black)
}

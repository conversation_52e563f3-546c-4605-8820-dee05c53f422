//
//  ChatInputView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct ChatInputView: View {
    @Binding var messageText: String
    var onSend: () -> Void
    var onTapToChat: (() -> Void)? = nil  // Optional callback for tap-to-chat navigation
    var disableNavigation: Bool = false  // Disable navigation when already in chat context

    @StateObject private var speechService = SpeechRecognitionService.shared
    @StateObject private var ttsService = TextToSpeechService.shared
    @State private var showVoiceRecording = false
    @State private var showChatScreen = false
    @State private var showAttachmentOptions = false
    @State private var showVoiceChatActive = false
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage? = nil
    @State private var imageSourceType: UIImagePickerController.SourceType = .photoLibrary
    @FocusState private var isTextFieldFocused: Bool

    var body: some View {
        VStack(spacing: 0) {
            // Image editing section (when image is selected)
            if let selectedImage = selectedImage {
                VStack(spacing: 12) {
                    HStack {
                        Text("Image Selected")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)

                        Spacer()

                        Button(action: {
                            self.selectedImage = nil
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.horizontal, 16)

                    // Image preview with editing options
                    HStack(spacing: 12) {
                        // Image preview
                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 60, height: 60)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )

                        // Editing options
                        VStack(alignment: .leading, spacing: 8) {
                            HStack(spacing: 16) {
                                Button(action: {
                                    // Crop functionality
                                }) {
                                    VStack(spacing: 4) {
                                        Image(systemName: "crop")
                                            .font(.system(size: 18))
                                            .foregroundColor(.blue)
                                        Text("Crop")
                                            .font(.system(size: 12))
                                            .foregroundColor(.blue)
                                    }
                                }

                                Button(action: {
                                    // Filter functionality
                                }) {
                                    VStack(spacing: 4) {
                                        Image(systemName: "camera.filters")
                                            .font(.system(size: 18))
                                            .foregroundColor(.blue)
                                        Text("Filter")
                                            .font(.system(size: 12))
                                            .foregroundColor(.blue)
                                    }
                                }

                                Button(action: {
                                    // Text overlay functionality
                                }) {
                                    VStack(spacing: 4) {
                                        Image(systemName: "textformat")
                                            .font(.system(size: 18))
                                            .foregroundColor(.blue)
                                        Text("Text")
                                            .font(.system(size: 12))
                                            .foregroundColor(.blue)
                                    }
                                }
                            }
                        }

                        Spacer()
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.05))
                .overlay(
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color.gray.opacity(0.2)),
                    alignment: .bottom
                )
            }

            ZStack {
                HStack {
                Button(action: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        showAttachmentOptions = true
                    }
                }) {
                    Image(systemName: "plus")
                        .font(.system(size: 20))
                        .foregroundColor(.gray)
                        .padding(8)
                        .background(Color.systemGray6)
                        .clipShape(Circle())
                }

                // Enhanced text field with modern design
                TextField("Write your message", text: $messageText)
                    .font(.system(size: 16, weight: .regular, design: .default))
                    .foregroundColor(.primary)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 14)
                    .background(
                        RoundedRectangle(cornerRadius: 22, style: .continuous)
                            .fill(Color.gray.opacity(0.1))
                            .overlay(
                                RoundedRectangle(cornerRadius: 22, style: .continuous)
                                    .stroke(isTextFieldFocused ? Color.blue.opacity(0.5) : Color.gray.opacity(0.2), lineWidth: 2)
                            )
                    )
                    .focused($isTextFieldFocused)
                    .onTapGesture {
                        // Only handle navigation if not disabled
                        if !disableNavigation {
                            // Use callback if provided, otherwise use built-in navigation
                            if let onTapToChat = onTapToChat {
                                onTapToChat()
                            } else {
                                // Fallback to built-in navigation
                                showChatScreen = true
                            }
                        } else {
                            // Just focus the text field when navigation is disabled
                            isTextFieldFocused = true
                        }
                    }
                    .onChange(of: speechService.recognizedText) { newValue in
                        if !newValue.isEmpty {
                            messageText = newValue
                        }
                    }

                Button(action: {
                    // Directly open camera
                    imageSourceType = .camera
                    showImagePicker = true
                }) {
                    Image(systemName: "camera")
                        .font(.system(size: 20))
                        .foregroundColor(.gray)
                }

                // Enhanced dynamic button with modern design
                Button(action: {
                    if messageText.isEmpty {
                        // Show voice recording interface
                        showVoiceRecording = true
                    } else {
                        // Add haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                        // Send the message
                        onSend()
                    }
                }) {
                    if messageText.isEmpty {
                        // Enhanced mic button when no text
                        Image(systemName: "mic.fill")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.white)
                            .frame(width: 44, height: 44)
                            .background(
                                Circle()
                                    .fill(LinearGradient(
                                        gradient: Gradient(colors: [Color.green, Color.green.opacity(0.8)]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ))
                                    .shadow(color: Color.green.opacity(0.3), radius: 4, x: 0, y: 2)
                            )
                    } else {
                        // Enhanced send button when there's text
                        Image(systemName: "arrow.up")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)
                            .frame(width: 36, height: 36)
                            .background(
                                Circle()
                                    .fill(LinearGradient(
                                        gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                        startPoint: .topLeading,
                                        endPoint: .bottomTrailing
                                    ))
                                    .shadow(color: Color.blue.opacity(0.3), radius: 4, x: 0, y: 2)
                            )
                    }
                }
                .scaleEffect(messageText.isEmpty ? 1.0 : 1.1)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: messageText.isEmpty)
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
            .padding(.bottom, 2) // Minimal bottom padding
            .background(
                VStack(spacing: 0) {
                    Rectangle()
                        .fill(Color.black.opacity(0.95))
                        .blur(radius: 10)
                        .overlay(
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                        )


                }
                .ignoresSafeArea(.all, edges: .bottom) // Extend to screen edge
            )


            // Full-screen voice recording view
            if showVoiceRecording {
                VoiceRecordingView(isPresented: $showVoiceRecording) { recognizedText in
                    if !recognizedText.isEmpty {
                        messageText = recognizedText

                        // Automatically send the message after a short delay
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            onSend()
                        }
                    }
                }
                .transition(.opacity)
                .zIndex(1)
            }

            // Attachment options popup
            if showAttachmentOptions {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            showAttachmentOptions = false
                        }
                    }
                    .zIndex(2)

                VStack(spacing: 0) {
                    Spacer()

                    VStack(spacing: 16) {
                        // Attach Photo
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                showAttachmentOptions = false
                            }
                            // Add photo attachment functionality
                        }) {
                            HStack(spacing: 16) {
                                Image(systemName: "photo")
                                    .font(.system(size: 20))
                                    .foregroundColor(.white)
                                    .frame(width: 40, height: 40)
                                    .background(Color.blue)
                                    .clipShape(Circle())

                                Text("Attach Photo")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color.systemGray6)
                            .cornerRadius(12)
                        }

                        // Take Photo
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                showAttachmentOptions = false
                            }
                            imageSourceType = .camera
                            showImagePicker = true
                        }) {
                            HStack(spacing: 16) {
                                Image(systemName: "viewfinder")
                                    .font(.system(size: 20))
                                    .foregroundColor(.white)
                                    .frame(width: 40, height: 40)
                                    .background(Color.orange)
                                    .clipShape(Circle())

                                Text("Take Photo")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color.systemGray6)
                            .cornerRadius(12)
                        }

                        // Attach Files
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                showAttachmentOptions = false
                            }
                            // Add file attachment functionality
                        }) {
                            HStack(spacing: 16) {
                                Image(systemName: "doc")
                                    .font(.system(size: 20))
                                    .foregroundColor(.white)
                                    .frame(width: 40, height: 40)
                                    .background(Color.green)
                                    .clipShape(Circle())

                                Text("Attach Files")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color.systemGray6)
                            .cornerRadius(12)
                        }

                        // Choose from Library
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                showAttachmentOptions = false
                            }
                            imageSourceType = .photoLibrary
                            showImagePicker = true
                        }) {
                            HStack(spacing: 16) {
                                Image(systemName: "photo")
                                    .font(.system(size: 20))
                                    .foregroundColor(.white)
                                    .frame(width: 40, height: 40)
                                    .background(Color.blue)
                                    .clipShape(Circle())

                                Text("Choose from Library")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color.systemGray6)
                            .cornerRadius(12)
                        }

                        // Voice Chat
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                showAttachmentOptions = false
                            }
                            showVoiceChatActive = true
                        }) {
                            HStack(spacing: 16) {
                                Image(systemName: "waveform")
                                    .font(.system(size: 20))
                                    .foregroundColor(.white)
                                    .frame(width: 40, height: 40)
                                    .background(Color.purple)
                                    .clipShape(Circle())

                                Text("Voice Chat")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                            .padding(.horizontal, 20)
                            .padding(.vertical, 16)
                            .background(Color.systemGray6)
                            .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 100) // Account for input bar height
                }
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .zIndex(3)
            }
        }
        // Only show fullScreenCover if no callback is provided (fallback behavior)
        .fullScreenCover(isPresented: $showChatScreen) {
            // Reset the message text when the chat screen is dismissed
            messageText = ""
        } content: {
            NavigationView {
                // When opening from text field tap, we don't want to auto-send
                // We pass an empty initial message and don't auto-focus to prevent keyboard from showing automatically
                ChatScreenView(initialMessage: "", autoSend: false)
            }
        }
        } // End VStack
        .fullScreenCover(isPresented: $showVoiceChatActive) {
            VoiceChatActiveView()
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage, sourceType: imageSourceType)
        }

        .onChange(of: selectedImage) { image in
            if let image = image {
                // Handle selected image - show editing section
                print("Image selected: \(image)")
                // Don't reset selectedImage here - let user manually remove it
            }
        }
        .alert(item: Binding<SpeechError?>(
            get: {
                if let error = speechService.errorMessage {
                    return SpeechError(message: error)
                }
                return nil
            },
            set: { _ in speechService.errorMessage = nil }
        )) { error in
            Alert(
                title: Text("Speech Recognition Error"),
                message: Text(error.message),
                dismissButton: .default(Text("OK"))
            )
        }
        // Removed automatic focus to prevent keyboard from showing automatically
    }
}



// Helper struct for speech errors
struct SpeechError: Identifiable {
    let id = UUID()
    let message: String
}

#Preview {
    ChatInputView(messageText: .constant(""), onSend: {})
        .preferredColorScheme(.dark)
}

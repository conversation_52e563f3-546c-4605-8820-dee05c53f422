//
//  ChatInputView.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct ChatInputView: View {
    @Binding var messageText: String
    var onSend: () -> Void
    var onTapToChat: (() -> Void)? = nil  // Optional callback for tap-to-chat navigation
    var disableNavigation: Bool = false  // Disable navigation when already in chat context

    @StateObject private var speechService = SpeechRecognitionService.shared
    @State private var showVoiceRecording = false
    @State private var showChatScreen = false
    @State private var showImagePicker = false
    @State private var selectedImage: UIImage? = nil
    @State private var imageSourceType: UIImagePickerController.SourceType = .photoLibrary
    @FocusState private var isTextFieldFocused: Bool

    var body: some View {
        VStack(spacing: 0) {
            // Image editing section (when image is selected)
            if let selectedImage = selectedImage {
                VStack(spacing: 12) {
                    HStack {
                        Text("Image Selected")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.primary)

                        Spacer()

                        Button(action: {
                            self.selectedImage = nil
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.gray)
                        }
                    }
                    .padding(.horizontal, 16)

                    // Image preview with editing options
                    HStack(spacing: 12) {
                        // Image preview
                        Image(uiImage: selectedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 60, height: 60)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )

                        // Editing options
                        VStack(alignment: .leading, spacing: 8) {
                            HStack(spacing: 16) {
                                Button(action: {
                                    // Crop functionality
                                }) {
                                    VStack(spacing: 4) {
                                        Image(systemName: "crop")
                                            .font(.system(size: 18))
                                            .foregroundColor(.blue)
                                        Text("Crop")
                                            .font(.system(size: 12))
                                            .foregroundColor(.blue)
                                    }
                                }

                                Button(action: {
                                    // Filter functionality
                                }) {
                                    VStack(spacing: 4) {
                                        Image(systemName: "camera.filters")
                                            .font(.system(size: 18))
                                            .foregroundColor(.blue)
                                        Text("Filter")
                                            .font(.system(size: 12))
                                            .foregroundColor(.blue)
                                    }
                                }

                                Button(action: {
                                    // Text overlay functionality
                                }) {
                                    VStack(spacing: 4) {
                                        Image(systemName: "textformat")
                                            .font(.system(size: 18))
                                            .foregroundColor(.blue)
                                        Text("Text")
                                            .font(.system(size: 12))
                                            .foregroundColor(.blue)
                                    }
                                }
                            }
                        }

                        Spacer()
                    }
                    .padding(.horizontal, 16)
                }
                .padding(.vertical, 12)
                .background(Color.gray.opacity(0.05))
                .overlay(
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color.gray.opacity(0.2)),
                    alignment: .bottom
                )
            }

            // Main input section - enhanced UI with modern design
            HStack(spacing: 14) {
                // Plus button with improved styling
                Button(action: {
                    // Handle attachment options
                    imageSourceType = .photoLibrary
                    showImagePicker = true
                }) {
                    Image(systemName: "plus")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)
                        .frame(width: 36, height: 36)
                        .background(
                            Circle()
                                .fill(Color(.systemGray6))
                                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                        )
                }

                // Text field with enhanced design
                TextField("Write your message", text: $messageText)
                    .font(.system(size: 16, weight: .regular))
                    .foregroundColor(.primary)
                    .padding(.horizontal, 18)
                    .padding(.vertical, 14)
                    .background(
                        RoundedRectangle(cornerRadius: 22)
                            .fill(Color(.systemGray6))
                            .shadow(color: .black.opacity(0.03), radius: 1, x: 0, y: 0.5)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 22)
                            .stroke(Color(.systemGray4).opacity(0.3), lineWidth: 0.5)
                    )
                    .focused($isTextFieldFocused)
                    .onTapGesture {
                        // Only handle navigation if not disabled
                        if !disableNavigation {
                            // Use callback if provided, otherwise use built-in navigation
                            if let onTapToChat = onTapToChat {
                                onTapToChat()
                            } else {
                                // Fallback to built-in navigation
                                showChatScreen = true
                            }
                        } else {
                            // Just focus the text field when navigation is disabled
                            isTextFieldFocused = true
                        }
                    }
                    .onChange(of: speechService.recognizedText) { newValue in
                        if !newValue.isEmpty {
                            messageText = newValue
                        }
                    }

                // Camera button with improved styling
                Button(action: {
                    imageSourceType = .camera
                    showImagePicker = true
                }) {
                    Image(systemName: "camera.fill")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.primary)
                        .frame(width: 36, height: 36)
                        .background(
                            Circle()
                                .fill(Color(.systemGray6))
                                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
                        )
                }

                // Voice/Send button with enhanced design
                Button(action: {
                    if messageText.isEmpty {
                        // Show voice recording interface
                        showVoiceRecording = true
                    } else {
                        // Add haptic feedback
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                        // Send the message
                        onSend()
                    }
                }) {
                    if messageText.isEmpty {
                        // Mic button when no text
                        Image(systemName: "mic.fill")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 38, height: 38)
                            .background(
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.green.opacity(0.9), Color.green]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .shadow(color: .green.opacity(0.3), radius: 4, x: 0, y: 2)
                            )
                    } else {
                        // Send button when there's text
                        Image(systemName: "arrow.up")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.white)
                            .frame(width: 38, height: 38)
                            .background(
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [Color.blue.opacity(0.9), Color.blue]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
                            )
                    }
                }
            }
            .padding(.horizontal, 18)
            .padding(.vertical, 14)
            .background(
                // Enhanced background with subtle gradient and blur effect
                RoundedRectangle(cornerRadius: 28)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color(.systemBackground).opacity(0.95),
                                Color(.systemGray6).opacity(0.8)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .shadow(color: .black.opacity(0.08), radius: 8, x: 0, y: 4)
                    .overlay(
                        RoundedRectangle(cornerRadius: 28)
                            .stroke(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.white.opacity(0.6),
                                        Color.clear
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
            .contentShape(Rectangle()) // Make entire area tappable
            .onTapGesture {
                // Only handle navigation if not disabled
                if !disableNavigation {
                    // Use callback if provided, otherwise use built-in navigation
                    if let onTapToChat = onTapToChat {
                        onTapToChat()
                    } else {
                        // Fallback to built-in navigation
                        showChatScreen = true
                    }
                } else {
                    // Just focus the text field when navigation is disabled
                    isTextFieldFocused = true
                }
            }
        }
        // Full-screen voice recording view
        .fullScreenCover(isPresented: $showVoiceRecording) {
            VoiceRecordingView(isPresented: $showVoiceRecording) { recognizedText in
                if !recognizedText.isEmpty {
                    messageText = recognizedText

                    // Automatically send the message after a short delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        onSend()
                    }
                }
            }
        }
        // Only show fullScreenCover if no callback is provided (fallback behavior)
        .fullScreenCover(isPresented: $showChatScreen) {
            // Reset the message text when the chat screen is dismissed
            messageText = ""
        } content: {
            NavigationView {
                // When opening from text field tap, we don't want to auto-send
                // We pass an empty initial message and don't auto-focus to prevent keyboard from showing automatically
                ChatScreenView(initialMessage: "", autoSend: false)
            }
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage, sourceType: imageSourceType)
        }
        .onChange(of: selectedImage) { image in
            if let image = image {
                // Handle selected image - show editing section
                print("Image selected: \(image)")
                // Don't reset selectedImage here - let user manually remove it
            }
        }
        .alert(item: Binding<SpeechError?>(
            get: {
                if let error = speechService.errorMessage {
                    return SpeechError(message: error)
                }
                return nil
            },
            set: { _ in speechService.errorMessage = nil }
        )) { error in
            Alert(
                title: Text("Speech Recognition Error"),
                message: Text(error.message),
                dismissButton: .default(Text("OK"))
            )
        }
    }
}



// Helper struct for speech errors
struct SpeechError: Identifiable {
    let id = UUID()
    let message: String
}

#Preview {
    ChatInputView(messageText: .constant(""), onSend: {})
        .preferredColorScheme(.dark)
}

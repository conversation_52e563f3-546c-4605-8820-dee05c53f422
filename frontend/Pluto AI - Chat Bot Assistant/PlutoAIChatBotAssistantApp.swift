//
//  PlutoAIChatBotAssistantApp.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025.04.30.
//  Updated by Augment on 2025.04.30.
//

import SwiftUI

@main
struct PlutoAIChatBotAssistantApp: App {
    let persistenceController = PersistenceController.shared

    init() {
        // Initialize device service for device-based authentication
        Task {
            await DeviceService.shared.initializeDevice()
        }
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .preferredColorScheme(.dark) // Force dark mode for the app
        }
    }
}

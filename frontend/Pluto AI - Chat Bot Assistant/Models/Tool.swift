//
//  Tool.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import SwiftUI

struct Tool: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let description: String
    let icon: String
    let assetImageName: String? // New property for asset image names
    let isNew: Bool
    let iconBackgroundColor: Color

    // Implement Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: Tool, rhs: Tool) -> Bool {
        return lhs.id == rhs.id
    }

    static let eliteTools: [Tool] = [
        Tool(name: "AI Image Generator", description: "Create & transform images with AI", icon: "photo.artframe", assetImageName: "aiimagegenerator", isNew: true, iconBackgroundColor: Color.orange),
        <PERSON>l(name: "Voice Chat", description: "Talk with Pluto AI about anything", icon: "mic.fill", assetImageName: nil, isNew: true, iconBackgroundColor: Color.purple),
        <PERSON><PERSON>(name: "AI Vision", description: "Analyze and understand images with AI", icon: "eye.fill", assetImageName: nil, isNew: true, iconBackgroundColor: Color.cyan),
        <PERSON><PERSON>(name: "Browsing Chat", description: "Get most recent answers with web search", icon: "globe", assetImageName: "browsingchat", isNew: false, iconBackgroundColor: Color.blue),
        Tool(name: "YouTube Summary", description: "Get detailed YouTube video summarization", icon: "play.rectangle.fill", assetImageName: "youtubesummary", isNew: false, iconBackgroundColor: Color.red),
        Tool(name: "Upload & Ask", description: "Search or ask about anything in a document", icon: "doc.text", assetImageName: "uploadandask", isNew: false, iconBackgroundColor: Color.gray),
        Tool(name: "Link & Ask", description: "Search or ask about anything in a webpage", icon: "link", assetImageName: "linkandask", isNew: false, iconBackgroundColor: Color.purple)
    ]

    // Elite tools for the Explore section - only show the top 3
    static let displayedEliteTools: [Tool] = Array(eliteTools.prefix(3))
}

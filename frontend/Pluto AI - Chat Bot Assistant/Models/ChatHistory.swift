//
//  ChatHistory.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import Foundation
import CoreData
import SwiftUI

struct ChatHistory: Identifiable {
    let id: UUID
    let title: String
    let description: String = "See your recent conversation"
    let date: Date
    var messages: [Message] = []
    var isSaved: Bool = false
    var modelName: String = "GPT-4.1 nano"

    // Initialize from a CoreData entity
    init(entity: ChatHistoryEntity) {
        self.id = entity.id ?? UUID()
        self.title = entity.title ?? "Chat"
        self.date = entity.date ?? Date()
        self.isSaved = entity.isSaved
        self.modelName = entity.modelName ?? "GPT-4.1 nano"

        // Convert messages if available
        if let messageEntities = entity.messages as? Set<MessageEntity> {
            self.messages = messageEntities.compactMap { Message(entity: $0) }
                .sorted { $0.timestamp < $1.timestamp }
        }
    }

    // Initialize manually
    init(id: UUID = UUID(), title: String, date: Date, isSaved: Bool = false, modelName: String = "GPT-4.1 nano") {
        self.id = id
        self.title = title
        self.date = date
        self.isSaved = isSaved
        self.modelName = modelName
    }

    // Save a new chat history to CoreData
    static func saveChat(title: String, messages: [Message], context: NSManagedObjectContext, modelName: String = "GPT-4.1 nano") {
        // Only save if there are messages and at least one is from the user
        guard !messages.isEmpty, messages.contains(where: { $0.isUser }) else { return }

        let newHistory = ChatHistoryEntity(context: context)
        newHistory.id = UUID()
        newHistory.title = title
        newHistory.date = Date()
        newHistory.isSaved = false
        newHistory.modelName = modelName

        // Save messages
        for message in messages {
            let messageEntity = MessageEntity(context: context)
            messageEntity.id = message.id
            messageEntity.content = message.content
            messageEntity.isUser = message.isUser
            messageEntity.timestamp = message.timestamp
            messageEntity.chatHistory = newHistory
        }

        // Save the context
        do {
            try context.save()
        } catch {
            print("Error saving chat history: \(error)")
        }
    }

    // Toggle saved status of a chat history
    static func toggleSaved(id: UUID, context: NSManagedObjectContext) {
        let fetchRequest: NSFetchRequest<ChatHistoryEntity> = ChatHistoryEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)

        do {
            let results = try context.fetch(fetchRequest)
            if let entity = results.first {
                entity.isSaved = !entity.isSaved
                try context.save()
            }
        } catch {
            print("Error toggling saved status: \(error)")
        }
    }

    // Get all chat histories from CoreData
    static func getAllChats(context: NSManagedObjectContext) -> [ChatHistory] {
        let fetchRequest: NSFetchRequest<ChatHistoryEntity> = ChatHistoryEntity.fetchRequest()
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \ChatHistoryEntity.date, ascending: false)]

        do {
            let results = try context.fetch(fetchRequest)
            return results.map { ChatHistory(entity: $0) }
        } catch {
            print("Error fetching chat histories: \(error)")
            return []
        }
    }

    // Delete a chat history from CoreData
    static func deleteChat(id: UUID, context: NSManagedObjectContext) {
        let fetchRequest: NSFetchRequest<ChatHistoryEntity> = ChatHistoryEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)

        do {
            let results = try context.fetch(fetchRequest)
            for entity in results {
                context.delete(entity)
            }
            try context.save()
        } catch {
            print("Error deleting chat history: \(error)")
        }
    }
}

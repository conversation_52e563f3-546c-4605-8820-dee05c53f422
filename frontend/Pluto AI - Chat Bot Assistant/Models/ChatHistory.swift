//
//  ChatHistory.swift
//  Pluto AI - <PERSON><PERSON> Assistant
//
//  Created by Augment on 2025.04.30.
//

import Foundation
import CoreData
import SwiftUI

struct ChatHistory: Identifiable {
    let id: UUID
    let title: String
    let description: String = "See your recent conversation"
    let date: Date
    var messages: [Message] = []
    var isSaved: Bool = false
    var modelName: String = "GPT-4.1 nano"

    // Initialize from a CoreData entity
    init(entity: ChatHistoryEntity) {
        self.id = entity.id ?? UUID()
        self.title = entity.title ?? "Chat"
        self.date = entity.date ?? Date()
        self.isSaved = entity.isSaved
        self.modelName = entity.modelName ?? "GPT-4.1 nano"

        // Convert messages if available
        if let messageEntities = entity.messages as? Set<MessageEntity> {
            self.messages = messageEntities.compactMap { Message(entity: $0) }
                .sorted { $0.timestamp < $1.timestamp }
        }
    }

    // Initialize manually
    init(id: UUID = UUID(), title: String, date: Date, isSaved: Bool = false, modelName: String = "GPT-4.1 nano") {
        self.id = id
        self.title = title
        self.date = date
        self.isSaved = isSaved
        self.modelName = modelName
    }

    // Save a new chat history to CoreData
    static func saveChat(title: String, messages: [Message], context: NSManagedObjectContext, modelName: String = "GPT-4.1 nano") {
        print("🔥 ChatHistory.saveChat called with title: '\(title)', messages count: \(messages.count)")

        // Only save if there are messages and at least one is from the user
        guard !messages.isEmpty, messages.contains(where: { $0.isUser }) else {
            print("🔥 ChatHistory.saveChat: No messages or no user messages, skipping save")
            return
        }

        // Use a default title if empty
        let finalTitle = title.isEmpty ? "New Chat" : title
        print("🔥 ChatHistory.saveChat: Using final title: '\(finalTitle)'")

        let newHistory = ChatHistoryEntity(context: context)
        newHistory.id = UUID()
        newHistory.title = finalTitle
        newHistory.date = Date()
        newHistory.isSaved = false
        newHistory.modelName = modelName

        print("🔥 ChatHistory.saveChat: Created ChatHistoryEntity with ID: \(newHistory.id?.uuidString ?? "nil")")

        // Save messages
        for message in messages {
            let messageEntity = MessageEntity(context: context)
            messageEntity.id = message.id
            messageEntity.content = message.content
            messageEntity.isUser = message.isUser
            messageEntity.timestamp = message.timestamp
            messageEntity.chatHistory = newHistory
            print("🔥 ChatHistory.saveChat: Added message: \(message.isUser ? "User" : "AI") - \(message.content.prefix(50))...")
        }

        // Save the context
        do {
            try context.save()
            print("🔥 ChatHistory.saveChat: Successfully saved chat history to Core Data")
        } catch {
            print("🔥 ChatHistory.saveChat: Error saving chat history: \(error)")
        }
    }

    // Toggle saved status of a chat history
    static func toggleSaved(id: UUID, context: NSManagedObjectContext) {
        let fetchRequest: NSFetchRequest<ChatHistoryEntity> = ChatHistoryEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)

        do {
            let results = try context.fetch(fetchRequest)
            if let entity = results.first {
                entity.isSaved = !entity.isSaved
                try context.save()
            }
        } catch {
            print("Error toggling saved status: \(error)")
        }
    }

    // Get all chat histories from CoreData
    static func getAllChats(context: NSManagedObjectContext) -> [ChatHistory] {
        print("🔥 ChatHistory.getAllChats: Fetching chat histories from Core Data")

        let fetchRequest: NSFetchRequest<ChatHistoryEntity> = ChatHistoryEntity.fetchRequest()
        fetchRequest.sortDescriptors = [NSSortDescriptor(keyPath: \ChatHistoryEntity.date, ascending: false)]

        do {
            let results = try context.fetch(fetchRequest)
            print("🔥 ChatHistory.getAllChats: Found \(results.count) chat history entities")

            let chatHistories = results.map { ChatHistory(entity: $0) }
            for (index, chat) in chatHistories.enumerated() {
                print("🔥 ChatHistory.getAllChats: Chat \(index + 1): '\(chat.title)' (\(chat.id)) - \(chat.messages.count) messages")
            }

            return chatHistories
        } catch {
            print("🔥 ChatHistory.getAllChats: Error fetching chat histories: \(error)")
            return []
        }
    }

    // Delete a chat history from CoreData
    static func deleteChat(id: UUID, context: NSManagedObjectContext) {
        let fetchRequest: NSFetchRequest<ChatHistoryEntity> = ChatHistoryEntity.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "id == %@", id as CVarArg)

        do {
            let results = try context.fetch(fetchRequest)
            for entity in results {
                context.delete(entity)
            }
            try context.save()
        } catch {
            print("Error deleting chat history: \(error)")
        }
    }
}

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional, Dict

from ..models import (
    Assistant, AssistantResponse,
    Tool, ToolResponse,
    Suggestion, SuggestionResponse,
    SuggestionCategory, SuggestionCategoryResponse,
    Color, SuggestedPrompt
)
from .auth import get_device_user, DeviceUser

router = APIRouter(
    prefix="/assistant",
    tags=["assistant"],
)

# Define all assistants
ASSISTANTS = [
    Assistant(
        name="Artist",
        description="Create stunning images with OpenAI's latest model.",
        icon_name="sparkles.rectangle.stack",
        background_color=Color(red=0.8, green=0.2, blue=0.8, opacity=1.0),  # Purple
        system_message="You are a helpful, creative, and friendly AI assistant named <PERSON>luto <PERSON>. You specialize in creating stunning images using OpenAI's GPT-Image-1 model. Help users craft detailed prompts that will generate high-quality, photorealistic images. When a user asks for an image, respond with a detailed description of what the image will look like, then generate the image using the GPT-Image-1 model with the prompt 'model': 'gpt-image-1'.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Create a photorealistic portrait of a futuristic cyberpunk character",
                icon="person.fill",
                description="Generate a detailed cyberpunk character with neon lighting"
            ),
            SuggestedPrompt(
                text="Design a minimalist logo for a tech startup",
                icon="sparkles",
                description="Create a clean, modern logo design"
            ),
            SuggestedPrompt(
                text="Generate a surreal landscape with floating islands",
                icon="mountain.2.fill",
                description="Create an imaginative fantasy landscape"
            )
        ]
    ),
    Assistant(
        name="Logo Designer",
        description="Unique and memorable logos for your brand.",
        icon_name="paintbrush.fill",
        background_color=Color(red=1.0, green=0.4, blue=0.7, opacity=1.0),  # Pink
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in creating unique and memorable logo designs. Provide detailed descriptions of logo concepts based on the user's brand and requirements.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Design a modern logo for a sustainable fashion brand",
                icon="leaf.fill",
                description="Create an eco-friendly brand identity"
            ),
            SuggestedPrompt(
                text="Create a tech company logo with geometric elements",
                icon="square.grid.3x3.fill",
                description="Design a clean, tech-focused logo"
            ),
            SuggestedPrompt(
                text="Design a vintage-style logo for a coffee shop",
                icon="cup.and.saucer.fill",
                description="Create a warm, inviting coffee brand"
            )
        ]
    ),
    Assistant(
        name="Creative Writer",
        description="Provides innovative writing ideas and drafts.",
        icon_name="pencil",
        background_color=Color(red=0.6, green=0.4, blue=0.2, opacity=1.0),  # Brown
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in creative writing. Help users with innovative writing ideas, drafts, and feedback on their writing.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Write a compelling opening for a sci-fi novel",
                icon="sparkles",
                description="Create an engaging science fiction story beginning"
            ),
            SuggestedPrompt(
                text="Help me develop a character for my fantasy story",
                icon="person.fill",
                description="Build a detailed fictional character"
            ),
            SuggestedPrompt(
                text="Create a plot twist for my mystery novel",
                icon="questionmark.circle.fill",
                description="Add an unexpected turn to your story"
            )
        ]
    ),
    Assistant(
        name="Business Planner",
        description="Provides strategies for business planning and growth.",
        icon_name="briefcase.fill",
        background_color=Color(red=0.0, green=0.0, blue=1.0, opacity=1.0),  # Blue
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in business planning and strategy. Provide advice on business growth, marketing, and operational efficiency.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Create a business plan for a mobile app startup",
                icon="iphone",
                description="Develop a comprehensive startup strategy"
            ),
            SuggestedPrompt(
                text="Analyze market opportunities for my product idea",
                icon="chart.line.uptrend.xyaxis",
                description="Research market potential and competition"
            ),
            SuggestedPrompt(
                text="Help me develop a pricing strategy for my service",
                icon="dollarsign.circle.fill",
                description="Create an effective pricing model"
            )
        ]
    ),
    Assistant(
        name="Study Helper",
        description="Assists with academic tasks and questions.",
        icon_name="book.fill",
        background_color=Color(red=0.5, green=0.0, blue=0.5, opacity=1.0),  # Purple
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in academic assistance. Help users with their studies, homework, research, and learning new concepts.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Explain quantum physics in simple terms",
                icon="atom",
                description="Break down complex physics concepts"
            ),
            SuggestedPrompt(
                text="Help me solve this calculus problem step by step",
                icon="function",
                description="Get detailed math problem solutions"
            ),
            SuggestedPrompt(
                text="Create a study plan for my upcoming exams",
                icon="calendar",
                description="Organize your study schedule effectively"
            )
        ]
    ),
    Assistant(
        name="Language Teacher",
        description="Check, translate or practice for a new language.",
        icon_name="character.bubble",
        background_color=Color(red=0.0, green=0.8, blue=0.8, opacity=1.0),  # Cyan
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in language teaching and translation. Help users learn new languages, practice their skills, and translate text between languages.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Teach me basic Spanish conversation phrases",
                icon="bubble.left.and.bubble.right.fill",
                description="Learn essential Spanish for daily conversations"
            ),
            SuggestedPrompt(
                text="Correct my French grammar in this paragraph",
                icon="checkmark.circle.fill",
                description="Get grammar feedback and corrections"
            ),
            SuggestedPrompt(
                text="Help me practice Japanese pronunciation",
                icon="speaker.wave.3.fill",
                description="Improve your Japanese speaking skills"
            )
        ]
    ),
    Assistant(
        name="Chef",
        description="Delicious recipe ideas and cooking tips.",
        icon_name="fork.knife",
        background_color=Color(red=1.0, green=0.5, blue=0.0, opacity=1.0),  # Orange
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in culinary arts. Provide delicious recipe ideas, cooking tips, and food-related advice to users.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Create a healthy meal plan for the week",
                icon="calendar",
                description="Plan nutritious meals for 7 days"
            ),
            SuggestedPrompt(
                text="Suggest a quick 30-minute dinner recipe",
                icon="clock.fill",
                description="Get fast and delicious dinner ideas"
            ),
            SuggestedPrompt(
                text="Help me bake the perfect chocolate cake",
                icon="birthday.cake.fill",
                description="Master the art of chocolate cake baking"
            )
        ]
    ),
    Assistant(
        name="Financial Analyst",
        description="Manage your finances and plan for a future.",
        icon_name="chart.line.uptrend.xyaxis",
        background_color=Color(red=0.0, green=0.8, blue=0.0, opacity=1.0),  # Green
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in financial analysis. Help users manage their finances, create budgets, and plan for their financial future."
    ),
    Assistant(
        name="Lawyer",
        description="Legal advice and document preparation.",
        icon_name="building.columns.fill",
        background_color=Color(red=0.4, green=0.2, blue=0.6, opacity=1.0),  # Purple-ish
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in legal matters. Provide general legal information and help with document preparation, while emphasizing that you're not a substitute for a licensed attorney."
    ),
    Assistant(
        name="Relationship Coach",
        description="Advice for healthy relationships and dating.",
        icon_name="heart.fill",
        background_color=Color(red=1.0, green=0.0, blue=0.0, opacity=1.0),  # Red
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in relationship coaching. Provide advice for healthy relationships, dating, and interpersonal communication."
    ),
    Assistant(
        name="Marketing Expert",
        description="Strategies to promote your business effectively.",
        icon_name="megaphone.fill",
        background_color=Color(red=1.0, green=0.5, blue=0.0, opacity=1.0),  # Orange
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in marketing. Provide strategies and advice to help users promote their businesses effectively."
    ),
    Assistant(
        name="Zodiac Expert",
        description="Astrological insights and horoscope readings.",
        icon_name="sparkles",
        background_color=Color(red=0.5, green=0.0, blue=0.5, opacity=1.0),  # Purple
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in astrology. Provide astrological insights and horoscope readings while acknowledging that astrology is for entertainment purposes."
    ),
    Assistant(
        name="Tattoo Artist",
        description="Design ideas and advice for tattoos.",
        icon_name="paintpalette.fill",
        background_color=Color(red=0.0, green=0.0, blue=0.0, opacity=1.0),  # Black
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in tattoo design. Provide creative tattoo ideas and advice on tattoo placement, styles, and aftercare."
    ),
    Assistant(
        name="Routine Planner",
        description="Help organize your daily activities efficiently.",
        icon_name="calendar",
        background_color=Color(red=0.0, green=0.0, blue=1.0, opacity=1.0),  # Blue
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in routine planning. Help users organize their daily activities efficiently and create effective schedules."
    ),
    Assistant(
        name="Interviewer",
        description="Prepare for job interviews with expert advice.",
        icon_name="person.2.fill",
        background_color=Color(red=0.5, green=0.5, blue=0.5, opacity=1.0),  # Gray
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in interview preparation. Help users prepare for job interviews with expert advice on common questions, responses, and interview techniques."
    ),
    Assistant(
        name="Wellness Advisor",
        description="Tips for mental and physical well-being.",
        icon_name="heart.text.square.fill",
        background_color=Color(red=0.0, green=0.8, blue=0.0, opacity=1.0),  # Green
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in wellness. Provide tips for mental and physical well-being, while emphasizing that you're not a substitute for professional medical advice."
    ),
    Assistant(
        name="Research Assistant",
        description="Help with finding and analyzing information.",
        icon_name="magnifyingglass",
        background_color=Color(red=0.0, green=0.0, blue=1.0, opacity=1.0),  # Blue
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in research. Help users find and analyze information on various topics."
    ),
    Assistant(
        name="Fitness & Health Coach",
        description="Personalized workout and nutrition plans.",
        icon_name="figure.run",
        background_color=Color(red=0.0, green=0.8, blue=0.0, opacity=1.0),  # Green
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in fitness and health. Provide personalized workout and nutrition plans while emphasizing that you're not a substitute for professional medical advice."
    ),
    Assistant(
        name="Travel Guide",
        description="Plan your perfect trip with local insights.",
        icon_name="airplane",
        background_color=Color(red=0.0, green=0.0, blue=1.0, opacity=1.0),  # Blue
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in travel planning. Help users plan their perfect trips with local insights, destination recommendations, and travel tips."
    ),
    Assistant(
        name="News Reporter",
        description="Stay updated with the latest news and events.",
        icon_name="newspaper.fill",
        background_color=Color(red=0.5, green=0.5, blue=0.5, opacity=1.0),  # Gray
        system_message="You are a helpful, creative, and friendly AI assistant named Pluto AI. You specialize in news reporting. Help users stay updated with the latest news and events, providing balanced and factual information."
    ),

    # AI Model-Specific Assistants
    Assistant(
        name="GPT-4.1 nano",
        description="Fast and efficient AI for everyday tasks.",
        icon_name="bolt.fill",
        background_color=Color(red=0.0, green=0.8, blue=0.4, opacity=1.0),  # Green
        system_message="You are GPT-4.1 nano, a fast and efficient AI assistant optimized for quick responses and everyday tasks. You excel at providing concise, helpful answers while maintaining high quality.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Summarize this article in 3 key points",
                icon="list.bullet",
                description="Get quick, concise summaries"
            ),
            SuggestedPrompt(
                text="Write a professional email response",
                icon="envelope.fill",
                description="Craft polished email replies"
            ),
            SuggestedPrompt(
                text="Explain this concept in simple terms",
                icon="lightbulb.fill",
                description="Break down complex ideas clearly"
            )
        ]
    ),
    Assistant(
        name="GPT-4.1 mini",
        description="Balanced performance for versatile conversations.",
        icon_name="cpu",
        background_color=Color(red=0.2, green=0.6, blue=0.9, opacity=1.0),  # Blue
        system_message="You are GPT-4.1 mini, a well-balanced AI assistant that offers excellent performance across a wide range of tasks. You provide thoughtful, detailed responses while maintaining efficiency.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Help me brainstorm creative solutions for this problem",
                icon="brain.head.profile",
                description="Generate innovative ideas and approaches"
            ),
            SuggestedPrompt(
                text="Analyze the pros and cons of this decision",
                icon="scale.3d",
                description="Get balanced decision-making insights"
            ),
            SuggestedPrompt(
                text="Create a detailed project plan",
                icon="list.clipboard.fill",
                description="Organize complex projects step-by-step"
            )
        ]
    ),
    Assistant(
        name="GPT-4.1",
        description="Advanced reasoning for complex challenges.",
        icon_name="brain",
        background_color=Color(red=0.6, green=0.2, blue=0.9, opacity=1.0),  # Purple
        system_message="You are GPT-4.1, an advanced AI assistant with superior reasoning capabilities. You excel at handling complex problems, deep analysis, and sophisticated conversations.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Conduct a comprehensive analysis of this business strategy",
                icon="chart.bar.xaxis",
                description="Deep dive into strategic planning"
            ),
            SuggestedPrompt(
                text="Help me solve this complex mathematical problem",
                icon="function",
                description="Tackle advanced mathematical challenges"
            ),
            SuggestedPrompt(
                text="Write a detailed research proposal",
                icon="doc.text.magnifyingglass",
                description="Create thorough academic documents"
            )
        ]
    ),
    Assistant(
        name="o4 - Mini",
        description="Optimized reasoning for logical tasks.",
        icon_name="gear.circle.fill",
        background_color=Color(red=0.9, green=0.5, blue=0.1, opacity=1.0),  # Orange
        system_message="You are o4-Mini, an AI assistant optimized for logical reasoning and problem-solving. You excel at step-by-step analysis and structured thinking.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Walk me through solving this logic puzzle",
                icon="puzzlepiece.fill",
                description="Step-by-step logical problem solving"
            ),
            SuggestedPrompt(
                text="Help me debug this code systematically",
                icon="wrench.and.screwdriver.fill",
                description="Systematic code troubleshooting"
            ),
            SuggestedPrompt(
                text="Create a decision tree for this choice",
                icon="arrow.triangle.branch",
                description="Structured decision-making framework"
            )
        ]
    ),
    Assistant(
        name="DeepSeek R1",
        description="Advanced reasoning with deep analytical capabilities.",
        icon_name="eye.circle.fill",
        background_color=Color(red=0.1, green=0.3, blue=0.7, opacity=1.0),  # Deep Blue
        system_message="You are DeepSeek R1, an advanced AI with exceptional reasoning and analytical capabilities. You excel at deep thinking, complex problem-solving, and providing insightful analysis.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Analyze the underlying patterns in this data",
                icon="chart.xyaxis.line",
                description="Deep data pattern recognition"
            ),
            SuggestedPrompt(
                text="Provide a philosophical perspective on this question",
                icon="brain.head.profile",
                description="Thoughtful philosophical analysis"
            ),
            SuggestedPrompt(
                text="Break down this complex system into components",
                icon="square.grid.3x3.square",
                description="Systems thinking and decomposition"
            )
        ]
    ),
    Assistant(
        name="DeepSeek V3",
        description="Versatile AI with broad knowledge and capabilities.",
        icon_name="sparkles.tv.fill",
        background_color=Color(red=0.2, green=0.5, blue=0.8, opacity=1.0),  # Blue
        system_message="You are DeepSeek V3, a versatile AI assistant with broad knowledge across multiple domains. You provide comprehensive, well-researched responses with creative insights.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Research and compare different approaches to this topic",
                icon="magnifyingglass.circle.fill",
                description="Comprehensive research and comparison"
            ),
            SuggestedPrompt(
                text="Generate creative alternatives to this solution",
                icon="lightbulb.max.fill",
                description="Creative problem-solving approaches"
            ),
            SuggestedPrompt(
                text="Explain the historical context of this event",
                icon="clock.arrow.circlepath",
                description="Historical analysis and context"
            )
        ]
    ),
    Assistant(
        name="Claude 3.7 Sonnet",
        description="Thoughtful and nuanced conversational AI.",
        icon_name="message.circle.fill",
        background_color=Color(red=0.8, green=0.4, blue=0.2, opacity=1.0),  # Orange-Brown
        system_message="You are Claude 3.7 Sonnet, an AI assistant known for thoughtful, nuanced conversations and careful reasoning. You excel at understanding context and providing balanced perspectives.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Help me think through the ethical implications of this decision",
                icon="balance.scale.fill",
                description="Ethical reasoning and moral considerations"
            ),
            SuggestedPrompt(
                text="Provide a nuanced analysis of this complex issue",
                icon="eye.trianglebadge.exclamationmark.fill",
                description="Balanced, multi-perspective analysis"
            ),
            SuggestedPrompt(
                text="Help me improve the tone and clarity of this writing",
                icon="pencil.and.outline",
                description="Writing refinement and style improvement"
            )
        ]
    ),
    Assistant(
        name="Claude 3.5 Haiku",
        description="Quick and creative AI for concise responses.",
        icon_name="quote.bubble.fill",
        background_color=Color(red=0.6, green=0.8, blue=0.3, opacity=1.0),  # Light Green
        system_message="You are Claude 3.5 Haiku, an AI assistant optimized for quick, creative, and concise responses. You excel at providing clear, helpful answers with a touch of creativity.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Write a creative haiku about this topic",
                icon="leaf.fill",
                description="Create beautiful, concise poetry"
            ),
            SuggestedPrompt(
                text="Give me a quick creative writing prompt",
                icon="pencil.tip.crop.circle.badge.plus",
                description="Spark your creative writing"
            ),
            SuggestedPrompt(
                text="Summarize this in a memorable way",
                icon="star.circle.fill",
                description="Create memorable, concise summaries"
            )
        ]
    ),
    Assistant(
        name="Gemini 2.0 Flash",
        description="Lightning-fast AI with multimodal capabilities.",
        icon_name="bolt.circle.fill",
        background_color=Color(red=0.9, green=0.2, blue=0.3, opacity=1.0),  # Red
        system_message="You are Gemini 2.0 Flash, a lightning-fast AI assistant with advanced multimodal capabilities. You excel at quick processing and understanding various types of content.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Quickly analyze this image and describe what you see",
                icon="photo.circle.fill",
                description="Fast image analysis and description"
            ),
            SuggestedPrompt(
                text="Give me rapid-fire facts about this topic",
                icon="speedometer",
                description="Quick knowledge delivery"
            ),
            SuggestedPrompt(
                text="Process this data and give me instant insights",
                icon="chart.bar.doc.horizontal.fill",
                description="Rapid data analysis"
            )
        ]
    ),
    Assistant(
        name="GPT-4o",
        description="Omni-modal AI for comprehensive tasks.",
        icon_name="circle.grid.cross.fill",
        background_color=Color(red=0.3, green=0.7, blue=0.5, opacity=1.0),  # Teal
        system_message="You are GPT-4o, an omni-modal AI assistant capable of handling text, images, and various content types. You provide comprehensive, detailed responses across multiple domains.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Analyze this document and extract key insights",
                icon="doc.text.magnifyingglass",
                description="Comprehensive document analysis"
            ),
            SuggestedPrompt(
                text="Create a detailed comparison between these options",
                icon="rectangle.split.2x1.fill",
                description="Thorough comparative analysis"
            ),
            SuggestedPrompt(
                text="Help me understand this complex topic from multiple angles",
                icon="viewfinder.circle.fill",
                description="Multi-perspective understanding"
            )
        ]
    ),
    Assistant(
        name="GPT-4o mini",
        description="Compact yet powerful AI for efficient tasks.",
        icon_name="smallcircle.filled.circle.fill",
        background_color=Color(red=0.4, green=0.6, blue=0.8, opacity=1.0),  # Light Blue
        system_message="You are GPT-4o mini, a compact yet powerful AI assistant optimized for efficiency. You provide high-quality responses while being resource-conscious.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Give me a concise but complete explanation",
                icon="text.alignleft",
                description="Efficient, complete explanations"
            ),
            SuggestedPrompt(
                text="Help me optimize this process for efficiency",
                icon="arrow.clockwise.circle.fill",
                description="Process optimization and improvement"
            ),
            SuggestedPrompt(
                text="Create a streamlined solution to this problem",
                icon="arrow.right.circle.fill",
                description="Streamlined problem-solving"
            )
        ]
    ),
    Assistant(
        name="o3",
        description="Next-generation reasoning for advanced challenges.",
        icon_name="brain.filled.head.profile",
        background_color=Color(red=0.7, green=0.1, blue=0.6, opacity=1.0),  # Magenta
        system_message="You are o3, a next-generation AI assistant with advanced reasoning capabilities. You excel at tackling the most challenging problems with sophisticated analysis.",
        suggested_prompts=[
            SuggestedPrompt(
                text="Solve this advanced reasoning challenge",
                icon="brain.head.profile.fill",
                description="Advanced logical reasoning"
            ),
            SuggestedPrompt(
                text="Provide a cutting-edge analysis of this problem",
                icon="scope",
                description="State-of-the-art problem analysis"
            ),
            SuggestedPrompt(
                text="Help me think through this complex theoretical question",
                icon="infinity.circle.fill",
                description="Advanced theoretical thinking"
            )
        ]
    )
]

@router.get("/assistants", response_model=AssistantResponse)
async def get_assistants():
    """Get all available assistants with their suggested prompts"""
    return AssistantResponse(assistants=ASSISTANTS)

@router.get("/assistants/{assistant_name}/prompts")
async def get_assistant_prompts(assistant_name: str):
    """Get suggested prompts for a specific assistant"""
    for assistant in ASSISTANTS:
        if assistant.name == assistant_name:
            return {
                "assistant_name": assistant_name,
                "suggested_prompts": assistant.suggested_prompts or []
            }

    return {
        "assistant_name": assistant_name,
        "suggested_prompts": []
    }

@router.get("/tools", response_model=ToolResponse)
async def get_tools():
    """Get all available tools"""
    return ToolResponse(tools=TOOLS)

@router.get("/suggestion-categories", response_model=SuggestionCategoryResponse)
async def get_suggestion_categories():
    """Get all suggestion categories"""
    return SuggestionCategoryResponse(categories=SUGGESTION_CATEGORIES)

@router.get("/suggestions", response_model=SuggestionResponse)
async def get_suggestions(category: Optional[str] = Query(None)):
    """Get suggestions, optionally filtered by category"""
    # Import suggestions from assistant_endpoints
    from .assistant_endpoints import CATEGORY_SUGGESTIONS

    if category:
        if category in CATEGORY_SUGGESTIONS:
            return SuggestionResponse(suggestions=CATEGORY_SUGGESTIONS[category])
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Category '{category}' not found"
            )

    # If no category specified, return all suggestions
    all_suggestions = []
    for suggestions in CATEGORY_SUGGESTIONS.values():
        all_suggestions.extend(suggestions)

    return SuggestionResponse(suggestions=all_suggestions)

@router.get("/system-message", response_model=dict)
async def get_system_message(
    assistant_name: Optional[str] = Query(None),
    tool_name: Optional[str] = Query(None),
    category_name: Optional[str] = Query(None),
    suggestion_text: Optional[str] = Query(None)
):
    """Get the system message for an assistant, tool, category, or suggestion"""

    if assistant_name:
        assistant = next((a for a in ASSISTANTS if a.name == assistant_name), None)
        if assistant:
            return {"system_message": assistant.system_message}

    if tool_name:
        tool = next((t for t in TOOLS if t.name == tool_name), None)
        if tool:
            return {"system_message": tool.system_message}

    if category_name:
        category = next((c for c in SUGGESTION_CATEGORIES if c.name == category_name), None)
        if category:
            return {"system_message": category.system_message}

    # Default system message
    return {"system_message": "You are a helpful, creative, and friendly AI assistant named Pluto AI."}

# Define all tools
TOOLS = [
    Tool(
        name="AI Image Generator",
        description="Create high-quality images with GPT-Image-1",
        icon="sparkles.rectangle.stack",
        is_new=True,
        icon_background_color=Color(red=0.8, green=0.2, blue=0.8, opacity=1.0),
        system_message="You are an expert on the AI Image Generator feature which now uses OpenAI's GPT-Image-1 model. This advanced model creates high-quality, photorealistic images from text prompts. Provide detailed information about how to use this tool, its capabilities, and best practices for creating stunning images with GPT-Image-1."
    ),
    Tool(
        name="Voice Chat",
        description="Talk with Pluto AI about anything",
        icon="waveform.circle",
        is_new=True,
        icon_background_color=Color(red=0.0, green=0.0, blue=0.0, opacity=1.0),
        system_message="You are an expert on the Voice Chat feature. Provide detailed information about how to use this tool, its capabilities, limitations, and best practices for talking with the AI using voice commands."
    ),
    Tool(
        name="Browsing Chat",
        description="Get most recent answers with web search",
        icon="globe",
        is_new=False,
        icon_background_color=Color(red=0.0, green=0.0, blue=1.0, opacity=1.0),
        system_message="You are an expert on the Browsing Chat feature. Provide detailed information about how to use this tool, its capabilities, limitations, and best practices for getting the most recent answers with web search."
    ),
    Tool(
        name="YouTube Summary",
        description="Get detailed YouTube video summarization",
        icon="play.rectangle.fill",
        is_new=False,
        icon_background_color=Color(red=1.0, green=0.0, blue=0.0, opacity=1.0),
        system_message="You are an expert on the YouTube Summary feature. Provide detailed information about how to use this tool, its capabilities, limitations, and best practices for getting detailed YouTube video summarizations."
    ),
    Tool(
        name="Upload & Ask",
        description="Search or ask about anything in a document",
        icon="doc.text",
        is_new=False,
        icon_background_color=Color(red=0.5, green=0.5, blue=0.5, opacity=1.0),
        system_message="You are an expert on the Upload & Ask feature. Provide detailed information about how to use this tool, its capabilities, limitations, and best practices for searching or asking about anything in a document."
    ),
    Tool(
        name="Link & Ask",
        description="Search or ask about anything in a webpage",
        icon="link",
        is_new=False,
        icon_background_color=Color(red=0.5, green=0.0, blue=0.5, opacity=1.0),
        system_message="You are an expert on the Link & Ask feature. Provide detailed information about how to use this tool, its capabilities, limitations, and best practices for searching or asking about anything in a webpage."
    )
]

# Define suggestion categories
SUGGESTION_CATEGORIES = [
    SuggestionCategory(
        name="E-Mail",
        icon="📧",
        color=Color(red=0.5, green=0.5, blue=0.5, opacity=1.0),  # Gray
        system_message="You are an expert email writer and communication specialist. Help the user craft professional, effective emails for various purposes. Provide clear, concise, and well-structured email content that achieves the user's goals."
    ),
    SuggestionCategory(
        name="Business & Marketing",
        icon="💼",
        color=Color(red=0.0, green=0.0, blue=1.0, opacity=1.0),  # Blue
        system_message="You are a business and marketing expert with extensive knowledge of strategy, market analysis, and growth tactics. Help the user with business plans, marketing strategies, and professional advice to achieve their business goals."
    ),
    SuggestionCategory(
        name="Astrology",
        icon="✨",
        color=Color(red=0.5, green=0.0, blue=0.5, opacity=1.0),  # Purple
        system_message="You are an astrology expert with deep knowledge of zodiac signs, horoscopes, and celestial influences. Provide insightful interpretations and guidance based on astrological principles while acknowledging that astrology is for entertainment purposes."
    ),
    SuggestionCategory(
        name="Education",
        icon="📚",
        color=Color(red=1.0, green=0.5, blue=0.0, opacity=1.0),  # Orange
        system_message="You are an educational expert and tutor with knowledge across multiple subjects. Help the user learn concepts, solve problems, and develop their understanding in a clear, patient, and supportive manner."
    ),
    SuggestionCategory(
        name="Art",
        icon="🎨",
        color=Color(red=1.0, green=0.4, blue=0.7, opacity=1.0),  # Pink
        system_message="You are a creative arts expert with knowledge of literature, music, visual arts, and creative writing. Help the user explore their creativity, develop artistic ideas, and appreciate various art forms."
    ),
    SuggestionCategory(
        name="Travel",
        icon="✈️",
        color=Color(red=0.0, green=0.8, blue=0.8, opacity=1.0),  # Cyan
        system_message="You are a travel expert with global knowledge of destinations, cultures, and travel planning. Provide helpful advice on itineraries, local attractions, cultural norms, and travel logistics to enhance the user's travel experiences."
    ),
    SuggestionCategory(
        name="Daily Lifestyle",
        icon="🌞",
        color=Color(red=0.0, green=0.8, blue=0.0, opacity=1.0),  # Green
        system_message="You are a lifestyle consultant with expertise in daily routines, personal organization, and balanced living. Provide practical advice to help the user improve their everyday life, habits, and overall wellbeing."
    ),
    SuggestionCategory(
        name="Relationship",
        icon="❤️",
        color=Color(red=1.0, green=0.0, blue=0.0, opacity=1.0),  # Red
        system_message="You are a relationship counselor with expertise in interpersonal dynamics, communication, and emotional intelligence. Provide thoughtful advice to help the user navigate their relationships in a healthy, respectful manner."
    ),
    SuggestionCategory(
        name="Fun",
        icon="🎮",
        color=Color(red=1.0, green=0.5, blue=0.0, opacity=1.0),  # Orange
        system_message="You are an entertainment specialist focused on providing fun, engaging, and lighthearted interactions. Your goal is to entertain the user with creative content, games, stories, and playful conversations."
    ),
    SuggestionCategory(
        name="Social",
        icon="👥",
        color=Color(red=0.0, green=0.0, blue=1.0, opacity=1.0),  # Blue
        system_message="You are a social interaction expert with knowledge of etiquette, communication skills, and social dynamics. Help the user navigate social situations, improve their social skills, and build meaningful connections."
    ),
    SuggestionCategory(
        name="Career",
        icon="💼",
        color=Color(red=0.5, green=0.5, blue=0.5, opacity=1.0),  # Gray
        system_message="You are a career counselor with expertise in professional development, job searching, and workplace success. Provide guidance to help the user advance their career, improve their skills, and achieve their professional goals."
    ),
    SuggestionCategory(
        name="Health & Nutrition",
        icon="🥗",
        color=Color(red=0.0, green=0.8, blue=0.0, opacity=1.0),  # Green
        system_message="You are a health and wellness advisor with knowledge of nutrition, fitness, and general wellbeing. Provide informative guidance while emphasizing that you're not a medical professional and serious health concerns should be directed to healthcare providers."
    ),
    SuggestionCategory(
        name="Greetings",
        icon="🎁",
        color=Color(red=1.0, green=0.0, blue=0.0, opacity=1.0),  # Red
        system_message="You are a specialist in creating heartfelt messages and greetings for special occasions. Help the user craft meaningful, personalized messages that convey their sentiments for celebrations, holidays, and important life events."
    )
]

import httpx
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from ..models import ChatMessage
from ..config import DEEPSEEK_API_KEY

class DeepSeekService:
    """Service for handling chat completions with DeepSeek API"""

    @staticmethod
    async def generate_response(messages: List[ChatMessage], model: str = "deepseek-chat") -> str:
        """Generate a response using DeepSeek's API"""

        # Create the request
        request_data = {
            "model": model,
            "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
            "temperature": 0.7,
            "max_tokens": 1000,
            "stream": False
        }

        # Send request to DeepSeek with extended timeout
        timeout = httpx.Timeout(60.0)  # 60 second timeout
        async with httpx.AsyncClient(timeout=timeout) as client:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
            }

            response = await client.post(
                "https://api.deepseek.com/v1/chat/completions",
                json=request_data,
                headers=headers
            )

            # Check for errors
            if response.status_code != 200:
                error_detail = response.json().get("error", {}).get("message", "Unknown error")
                raise Exception(f"DeepSeek API error: {error_detail}")

            # Parse the response
            response_data = response.json()

            # Extract the message content
            if "choices" in response_data and len(response_data["choices"]) > 0:
                return response_data["choices"][0]["message"]["content"]
            else:
                return "I couldn't generate a response. Please try again."

    @staticmethod
    async def generate_streaming_response(messages: List[ChatMessage], model: str = "deepseek-chat") -> AsyncGenerator[str, None]:
        """Generate a streaming response using DeepSeek's API"""

        # Create the request
        request_data = {
            "model": model,
            "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
            "temperature": 0.7,
            "max_tokens": 1000,
            "stream": True
        }

        # Send request to DeepSeek with extended timeout
        timeout = httpx.Timeout(60.0)  # 60 second timeout
        async with httpx.AsyncClient(timeout=timeout) as client:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {DEEPSEEK_API_KEY}"
            }

            async with client.stream(
                "POST",
                "https://api.deepseek.com/v1/chat/completions",
                json=request_data,
                headers=headers
            ) as response:

                # Check for errors
                if response.status_code != 200:
                    error_detail = "DeepSeek API error"
                    try:
                        error_data = await response.aread()
                        error_json = json.loads(error_data)
                        error_detail = error_json.get("error", {}).get("message", "Unknown error")
                    except:
                        pass
                    raise Exception(f"DeepSeek API error: {error_detail}")

                # Process streaming response
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix

                        if data == "[DONE]":
                            break

                        try:
                            chunk_data = json.loads(data)
                            if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                                delta = chunk_data["choices"][0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    yield content
                        except json.JSONDecodeError:
                            continue

import httpx
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from ..models import ChatMessage, ChatCompletionRequest
from ..config import OPENAI_API_KEY

class ChatService:
    """Service for handling chat completions with OpenAI API"""

    @staticmethod
    def _is_reasoning_model(model: str) -> bool:
        """Check if the model is a reasoning model that requires max_completion_tokens"""
        reasoning_models = ["o1", "o3", "o4", "gpt-o4"]
        return any(reasoning_model in model.lower() for reasoning_model in reasoning_models)

    @staticmethod
    async def generate_response(messages: List[ChatMessage], model: str = "gpt-4o-mini") -> str:
        """Generate a response using OpenAI's API"""

        # Determine which token parameter to use and temperature
        if ChatService._is_reasoning_model(model):
            # Filter out system messages for reasoning models (they don't support them)
            filtered_messages = [{"role": msg.role, "content": msg.content} for msg in messages if msg.role != "system"]
            # Use max_completion_tokens for reasoning models and default temperature (1.0)
            request_data = ChatCompletionRequest(
                model=model,
                messages=filtered_messages,
                temperature=1.0,  # Reasoning models only support default temperature
                max_completion_tokens=1000,
                stream=False
            )
        else:
            # Use max_tokens for regular models with custom temperature
            request_data = ChatCompletionRequest(
                model=model,
                messages=[{"role": msg.role, "content": msg.content} for msg in messages],
                temperature=0.7,
                max_tokens=1000,
                stream=False
            )

        # Convert to dict for httpx and exclude None values
        request_json = request_data.model_dump(exclude_none=True)

        # Debug: Print request details
        print(f"ChatService: Sending request to OpenAI")
        print(f"Model: {request_json.get('model')}")
        print(f"Messages count: {len(request_json.get('messages', []))}")
        print(f"First message: {request_json.get('messages', [{}])[0] if request_json.get('messages') else 'None'}")

        # Send request to OpenAI with extended timeout
        timeout = httpx.Timeout(60.0)  # 60 second timeout
        async with httpx.AsyncClient(timeout=timeout) as client:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {OPENAI_API_KEY}"
            }

            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                json=request_json,
                headers=headers
            )

            # Check for errors
            if response.status_code != 200:
                error_detail = response.json().get("error", {}).get("message", "Unknown error")
                raise Exception(f"OpenAI API error: {error_detail}")

            # Parse the response
            response_data = response.json()

            # Extract the message content
            if "choices" in response_data and len(response_data["choices"]) > 0:
                return response_data["choices"][0]["message"]["content"]
            else:
                return "I couldn't generate a response. Please try again."

    @staticmethod
    def create_system_message(assistant_type: str) -> ChatMessage:
        """Create a system message based on the assistant type"""

        base_prompt = "You are a helpful, creative, and friendly AI assistant named Pluto AI."

        # Customize based on assistant type
        if assistant_type == "Logo Designer":
            prompt = f"{base_prompt} You specialize in creating unique and memorable logo designs. Provide detailed descriptions of logo concepts based on the user's brand and requirements."
        elif assistant_type == "Creative Writer":
            prompt = f"{base_prompt} You specialize in creative writing. Help users with innovative writing ideas, drafts, and feedback on their writing."
        elif assistant_type == "Business Planner":
            prompt = f"{base_prompt} You specialize in business planning and strategy. Provide advice on business growth, marketing, and operational efficiency."
        # Add more assistant types as needed
        else:
            prompt = base_prompt

        return ChatMessage(role="system", content=prompt)

    @staticmethod
    def convert_messages(messages: List[Dict[str, Any]], assistant_type: str, system_message: Optional[str] = None) -> List[ChatMessage]:
        """Convert app messages to API format with custom system message"""

        # Start with system message
        if system_message:
            api_messages = [ChatMessage(role="system", content=system_message)]
        else:
            api_messages = [ChatService.create_system_message(assistant_type)]

        # Add user and assistant messages
        for message in messages:
            role = "user" if message.get("is_user", False) else "assistant"
            api_messages.append(ChatMessage(role=role, content=message.get("content", "")))

        return api_messages

    @staticmethod
    async def generate_streaming_response(messages: List[ChatMessage], model: str = "gpt-4o-mini") -> AsyncGenerator[str, None]:
        """Generate a streaming response using OpenAI's API"""

        # Determine which token parameter to use and temperature
        if ChatService._is_reasoning_model(model):
            # Filter out system messages for reasoning models (they don't support them)
            filtered_messages = [{"role": msg.role, "content": msg.content} for msg in messages if msg.role != "system"]
            # Use max_completion_tokens for reasoning models and default temperature (1.0)
            request_data = ChatCompletionRequest(
                model=model,
                messages=filtered_messages,
                temperature=1.0,  # Reasoning models only support default temperature
                max_completion_tokens=1000,
                stream=True  # Enable streaming
            )
        else:
            # Use max_tokens for regular models with custom temperature
            request_data = ChatCompletionRequest(
                model=model,
                messages=[{"role": msg.role, "content": msg.content} for msg in messages],
                temperature=0.7,
                max_tokens=1000,
                stream=True  # Enable streaming
            )

        # Convert to dict for httpx and exclude None values
        request_json = request_data.model_dump(exclude_none=True)

        # Send request to OpenAI
        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {OPENAI_API_KEY}"
            }

            async with client.stream(
                "POST",
                "https://api.openai.com/v1/chat/completions",
                json=request_json,
                headers=headers
            ) as response:

                # Check for errors
                if response.status_code != 200:
                    error_text = await response.aread()
                    try:
                        error_data = json.loads(error_text)
                        error_detail = error_data.get("error", {}).get("message", "Unknown error")
                    except:
                        error_detail = f"HTTP {response.status_code}"
                    raise Exception(f"OpenAI API error: {error_detail}")

                # Process streaming response
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix

                        if data == "[DONE]":
                            break

                        try:
                            chunk_data = json.loads(data)
                            if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                                delta = chunk_data["choices"][0].get("delta", {})
                                content = delta.get("content", "")
                                if content:
                                    yield content
                        except json.JSONDecodeError:
                            continue

import httpx
import json
from typing import List, Dict, Any, Optional, AsyncGenerator
from sqlalchemy.orm import Session
from ..models import ChatMessage, ChatCompletionRequest
from ..config import OPENAI_API_KEY
from .memory_service import MemoryService, MemoryExtractionService
from .chat_service import ChatService

class EnhancedChatService:
    """Enhanced chat service with memory integration for personalized responses"""
    
    def __init__(self, db: Session):
        self.db = db
        self.memory_service = MemoryService(db)
        self.extraction_service = MemoryExtractionService(db)
        self.base_chat_service = ChatService()
    
    async def generate_personalized_response(
        self, 
        user_id: str,
        device_id: str,
        messages: List[ChatMessage], 
        model: str = "gpt-4o-mini",
        assistant_id: Optional[str] = None,
        extract_memories: bool = True
    ) -> str:
        """Generate a personalized response using user memories"""
        
        # Get the user's message (last message should be from user)
        user_message = ""
        for msg in reversed(messages):
            if msg.role == "user":
                user_message = msg.content
                break
        
        # Get relevant memories for context
        relevant_memories = self.memory_service.get_relevant_memories(
            user_id, user_message, limit=5
        )
        
        # Enhance the system message with memory context
        enhanced_messages = self._add_memory_context(messages, relevant_memories)
        
        # Generate response using the base chat service
        response = await self.base_chat_service.generate_response(enhanced_messages, model)
        
        # Extract new memories from the conversation if enabled
        if extract_memories and user_message:
            try:
                conversation_text = self._format_conversation_for_extraction(messages, response)
                await self.extraction_service.extract_memories_from_conversation(
                    user_id=user_id,
                    device_id=device_id,
                    conversation_text=conversation_text,
                    context=f"Assistant: {assistant_id}" if assistant_id else None,
                    assistant_id=assistant_id
                )
            except Exception as e:
                print(f"Error extracting memories: {e}")
        
        return response
    
    async def generate_personalized_streaming_response(
        self, 
        user_id: str,
        device_id: str,
        messages: List[ChatMessage], 
        model: str = "gpt-4o-mini",
        assistant_id: Optional[str] = None,
        extract_memories: bool = True
    ) -> AsyncGenerator[str, None]:
        """Generate a personalized streaming response using user memories"""
        
        # Get the user's message (last message should be from user)
        user_message = ""
        for msg in reversed(messages):
            if msg.role == "user":
                user_message = msg.content
                break
        
        # Get relevant memories for context
        relevant_memories = self.memory_service.get_relevant_memories(
            user_id, user_message, limit=5
        )
        
        # Enhance the system message with memory context
        enhanced_messages = self._add_memory_context(messages, relevant_memories)
        
        # Store the complete response for memory extraction
        complete_response = ""
        
        # Generate streaming response using the base chat service
        async for chunk in self.base_chat_service.generate_streaming_response(enhanced_messages, model):
            complete_response += chunk
            yield chunk
        
        # Extract new memories from the conversation if enabled
        if extract_memories and user_message:
            try:
                conversation_text = self._format_conversation_for_extraction(messages, complete_response)
                await self.extraction_service.extract_memories_from_conversation(
                    user_id=user_id,
                    device_id=device_id,
                    conversation_text=conversation_text,
                    context=f"Assistant: {assistant_id}" if assistant_id else None,
                    assistant_id=assistant_id
                )
            except Exception as e:
                print(f"Error extracting memories: {e}")
    
    def _add_memory_context(self, messages: List[ChatMessage], memories: List[Any]) -> List[ChatMessage]:
        """Add memory context to the conversation"""
        if not memories:
            return messages
        
        # Find the system message
        system_message_index = -1
        for i, msg in enumerate(messages):
            if msg.role == "system":
                system_message_index = i
                break
        
        # Create memory context
        memory_context = self._format_memory_context(memories)
        
        if system_message_index >= 0:
            # Enhance existing system message
            original_content = messages[system_message_index].content
            enhanced_content = f"{original_content}\n\n{memory_context}"
            
            # Create new list with enhanced system message
            enhanced_messages = messages.copy()
            enhanced_messages[system_message_index] = ChatMessage(
                role="system", 
                content=enhanced_content
            )
            return enhanced_messages
        else:
            # Add new system message with memory context
            memory_system_message = ChatMessage(
                role="system",
                content=f"You are a helpful AI assistant. Use the following information about the user to provide personalized responses:\n\n{memory_context}"
            )
            return [memory_system_message] + messages
    
    def _format_memory_context(self, memories: List[Any]) -> str:
        """Format memories into context for the AI"""
        if not memories:
            return ""
        
        context_parts = ["## User Information:"]
        
        # Group memories by type
        memory_groups = {}
        for memory in memories:
            memory_type = memory.memory_type
            if memory_type not in memory_groups:
                memory_groups[memory_type] = []
            memory_groups[memory_type].append(memory)
        
        # Format each group
        type_labels = {
            "personal_fact": "Personal Facts",
            "preference": "Preferences",
            "goal": "Goals & Aspirations",
            "context": "Recent Context",
            "behavior": "Communication Style",
            "technical": "Technical Background"
        }
        
        for memory_type, group_memories in memory_groups.items():
            label = type_labels.get(memory_type, memory_type.replace("_", " ").title())
            context_parts.append(f"\n### {label}:")
            
            for memory in group_memories:
                context_parts.append(f"- {memory.title}: {memory.content}")
        
        context_parts.append("\nUse this information to provide more personalized and relevant responses. Don't explicitly mention that you're using stored information unless directly asked.")
        
        return "\n".join(context_parts)
    
    def _format_conversation_for_extraction(self, messages: List[ChatMessage], ai_response: str) -> str:
        """Format the conversation for memory extraction"""
        conversation_parts = []
        
        for msg in messages:
            if msg.role == "user":
                conversation_parts.append(f"User: {msg.content}")
            elif msg.role == "assistant":
                conversation_parts.append(f"Assistant: {msg.content}")
            # Skip system messages for extraction
        
        # Add the latest AI response
        conversation_parts.append(f"Assistant: {ai_response}")
        
        return "\n\n".join(conversation_parts)
    
    async def get_conversation_summary(self, user_id: str, messages: List[ChatMessage]) -> Optional[str]:
        """Generate a summary of the conversation for memory purposes"""
        if len(messages) < 2:
            return None
        
        # Format conversation
        conversation_text = ""
        for msg in messages:
            if msg.role in ["user", "assistant"]:
                role = "User" if msg.role == "user" else "Assistant"
                conversation_text += f"{role}: {msg.content}\n\n"
        
        # Create summary prompt
        summary_messages = [
            ChatMessage(
                role="system",
                content="Summarize this conversation in 2-3 sentences, focusing on the main topics discussed and any important information about the user."
            ),
            ChatMessage(
                role="user",
                content=f"Conversation to summarize:\n\n{conversation_text}"
            )
        ]
        
        try:
            summary = await self.base_chat_service.generate_response(summary_messages, "gpt-4o-mini")
            return summary
        except Exception as e:
            print(f"Error generating conversation summary: {e}")
            return None
    
    async def analyze_user_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analyze user communication patterns and preferences"""
        # Get recent memories
        from ..models import MemorySearchRequest, MemoryType
        from datetime import datetime, timedelta
        
        # Search for recent behavioral and preference memories
        search_request = MemorySearchRequest(
            memory_types=[MemoryType.BEHAVIOR, MemoryType.PREFERENCE],
            limit=20
        )
        
        result = self.memory_service.search_memories(user_id, search_request)
        memories = result["memories"]
        
        # Analyze patterns
        patterns = {
            "communication_style": "friendly",
            "preferred_response_length": "medium",
            "technical_level": "intermediate",
            "common_topics": [],
            "interaction_frequency": "regular"
        }
        
        # Extract patterns from memories
        for memory in memories:
            if "technical" in memory.content.lower():
                patterns["technical_level"] = "advanced"
            elif "simple" in memory.content.lower() or "basic" in memory.content.lower():
                patterns["technical_level"] = "beginner"
            
            if "brief" in memory.content.lower() or "short" in memory.content.lower():
                patterns["preferred_response_length"] = "short"
            elif "detailed" in memory.content.lower() or "comprehensive" in memory.content.lower():
                patterns["preferred_response_length"] = "long"
        
        return patterns
